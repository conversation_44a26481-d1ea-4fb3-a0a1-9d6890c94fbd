# Real-time Firebase Listeners Documentation

## Overview

This documentation covers the enhanced real-time Firebase listener system that provides live updates for chats, messages, and comments with automatic read receipt management.

## Architecture

### Components

1. **RealtimeManager** (`src/services/firebase/realtimeManager.js`)
   - Singleton service managing all Firebase listeners
   - Handles listener lifecycle, cleanup, and read receipt batching
   - Provides centralized listener management

2. **useRealtimeChat Hook** (`src/hooks/useRealtimeChat.js`)
   - React hook for easy integration with components
   - Automatic listener setup based on Redux state
   - Provides utilities for managing real-time functionality

3. **Redux Actions** (`src/redux/features/metaBusinessChatSlice.js`)
   - Enhanced async thunks for real-time operations
   - Integrated with existing Redux state management
   - Automatic state updates from real-time events

4. **RealtimeStatus Component** (`src/components/RealtimeStatus/RealtimeStatus.jsx`)
   - Visual status indicator for active listeners
   - Debug controls for managing listeners
   - Compact and expanded view modes

## Features

### 🔄 Real-time Updates
- **Chats**: Live updates for all chats in a page
- **Messages**: Real-time message updates for active chat
- **Comments**: Live comment updates with read receipts

### 📖 Enhanced Read Receipts
- **Automatic marking**: Comments marked as read when viewed
- **Batched updates**: Read receipts batched for performance
- **Real-time sync**: Read receipts updated across all clients
- **Hybrid approach**: Backend + Firebase synchronization

### 🎯 Smart Listener Management
- **Automatic cleanup**: Listeners cleaned up when not needed
- **State-based activation**: Listeners start/stop based on UI state
- **Performance monitoring**: Built-in performance tracking
- **Error handling**: Graceful degradation on errors

## Usage

### Basic Setup with Hook

```jsx
import { useRealtimeChat } from '../hooks/useRealtimeChat';

const ChatComponent = () => {
  const {
    startChatsListener,
    startMessagesListener,
    startCommentsListener,
    markCommentsAsRead,
    isChatsListenerActive,
    isMessagesListenerActive,
    isCommentsListenerActive
  } = useRealtimeChat({
    autoStartChatsListener: true,
    autoStartMessagesListener: true,
    autoStartCommentsListener: false,
    autoMarkCommentsAsRead: true,
    cleanupOnUnmount: true
  });

  // Listeners will start automatically based on options
  // Manual control available through returned functions
};
```

### Manual Listener Management

```jsx
import { useDispatch } from 'react-redux';
import {
  startRealtimeChatsListener,
  startRealtimeMessagesListener,
  startRealtimeCommentsListener,
  stopAllRealtimeListeners
} from '../redux/features/metaBusinessChatSlice';

const ManualComponent = () => {
  const dispatch = useDispatch();

  const startListeners = async () => {
    // Start chats listener for current page
    await dispatch(startRealtimeChatsListener({ pageId: '123' }));

    // Start messages listener for specific chat
    await dispatch(startRealtimeMessagesListener({
      chatId: 'chat_456',
      chatType: 'messenger',
      pageId: '123'
    }));

    // Start comments listener with auto-read
    await dispatch(startRealtimeCommentsListener({
      chatId: 'chat_456',
      chatType: 'messenger',
      pageId: '123',
      autoMarkAsRead: true
    }));
  };

  const stopListeners = async () => {
    await dispatch(stopAllRealtimeListeners());
  };
};
```

### Direct RealtimeManager Usage

```jsx
import realtimeManager from '../services/firebase/realtimeManager';

// Start listening to all chats for a page
const unsubscribeChats = realtimeManager.listenToAllChats(
  'page_123',
  (chats, error) => {
    if (error) {
      console.error('Chats listener error:', error);
      return;
    }
    console.log('Received chat updates:', chats);
  }
);

// Start listening to messages for a chat
const unsubscribeMessages = realtimeManager.listenToMessages(
  'chat_456',
  'messenger',
  'page_123',
  (messages, error) => {
    if (error) {
      console.error('Messages listener error:', error);
      return;
    }
    console.log('Received message updates:', messages);
  }
);

// Start listening to comments with auto-read
const unsubscribeComments = realtimeManager.listenToComments(
  'chat_456',
  'messenger',
  'page_123',
  (comments, error) => {
    if (error) {
      console.error('Comments listener error:', error);
      return;
    }
    console.log('Received comment updates:', comments);
  },
  { id: 'user_123', name: 'John Doe' },
  { autoMarkAsRead: true }
);

// Manual read receipt marking
await realtimeManager.markCommentsAsReadInFirebase(
  'chat_456',
  'messenger',
  'page_123',
  ['comment_1', 'comment_2'],
  { id: 'user_123', name: 'John Doe' }
);

// Cleanup
unsubscribeChats();
unsubscribeMessages();
unsubscribeComments();
```

## Integration Examples

### Enhanced CommentsModal

The CommentsModal has been updated to use the new real-time system:

```jsx
// Real-time status indicator in header
{isCommentsListenerActive && (
  <span className="realtime-status-indicator">
    ● LIVE
  </span>
)}

// Automatic listener setup
useEffect(() => {
  if (isOpen && currentUser) {
    // Load initial comments
    dispatch(loadChatCommentsHybrid({ ... }));

    // Start real-time listener
    startCommentsListener();
  }
}, [isOpen, currentUser, startCommentsListener]);

// Enhanced read receipt handling
useEffect(() => {
  if (unreadComments.length > 0) {
    // Real-time marking
    markCommentsAsRead(unreadCommentIds);

    // Backend sync
    dispatch(markCommentsAsReadHybrid({ ... }));
  }
}, [comments, markCommentsAsRead]);
```

### Status Monitoring

```jsx
import RealtimeStatus from '../components/RealtimeStatus/RealtimeStatus';

// Compact status indicator
<RealtimeStatus compact={true} />

// Full status with controls
<RealtimeStatus showControls={true} />

// Get programmatic status
const { getListenerStatus } = useRealtimeChat();
const status = await getListenerStatus();
console.log('Active listeners:', status.totalListeners);
```

## Configuration

### Hook Options

```jsx
const options = {
  autoStartChatsListener: true,     // Auto-start chats listener when page selected
  autoStartMessagesListener: true,  // Auto-start messages listener when chat selected
  autoStartCommentsListener: false, // Auto-start comments listener when modal opens
  autoMarkCommentsAsRead: true,     // Automatically mark comments as read
  cleanupOnUnmount: true           // Cleanup listeners when component unmounts
};
```

### RealtimeManager Options

```jsx
// Listener options
const listenerOptions = {
  enablePerformanceMonitoring: true,
  batchUpdates: true,
  autoScrollToBottom: true,
  batchReadReceipts: true
};

// Read receipt options
const readReceiptOptions = {
  autoMarkAsRead: true,
  batchDelay: 1000,  // Delay before processing batched receipts
  maxBatchSize: 50   // Maximum receipts per batch
};
```

## Performance Considerations

### Listener Lifecycle
- Listeners are automatically cleaned up when not needed
- State changes trigger appropriate listener start/stop
- Duplicate listeners are prevented

### Read Receipt Batching
- Read receipts are batched to reduce Firebase writes
- 1-second delay before processing batched receipts
- Prevents excessive API calls during rapid comment viewing

### Memory Management
- Singleton RealtimeManager prevents memory leaks
- Automatic cleanup on component unmount
- Listener references properly managed

## Troubleshooting

### Common Issues

1. **Listeners not starting**
   - Check if pageId is available in Redux state
   - Verify user authentication
   - Check console for error messages

2. **Read receipts not updating**
   - Ensure user has proper permissions
   - Check Firebase security rules
   - Verify pageId parameter is passed correctly

3. **Performance issues**
   - Monitor listener count with RealtimeStatus
   - Check for duplicate listeners
   - Use compact status indicator in production

### Debug Tools

```jsx
// Get listener status
const status = await getListenerStatus();
console.log('Debug info:', status);

// Monitor active listeners
console.log('Active listeners:', activeListeners);

// Check listener state
console.log('Chats active:', isChatsListenerActive);
console.log('Messages active:', isMessagesListenerActive);
console.log('Comments active:', isCommentsListenerActive);
```

## Migration Guide

### From Old System

1. Replace direct Firebase listener setup with useRealtimeChat hook
2. Update read receipt logic to use new markCommentsAsRead function
3. Add RealtimeStatus component for monitoring
4. Remove manual listener cleanup code (handled automatically)

### Example Migration

```jsx
// Before
useEffect(() => {
  const unsubscribe = onSnapshot(collection(db, 'comments'), callback);
  return () => unsubscribe();
}, []);

// After
const { startCommentsListener } = useRealtimeChat({
  autoStartCommentsListener: true
});
```

## Best Practices

1. **Use the hook**: Prefer useRealtimeChat over direct RealtimeManager usage
2. **Monitor performance**: Use RealtimeStatus in development
3. **Handle errors**: Always check for errors in callbacks
4. **Cleanup properly**: Let the system handle cleanup automatically
5. **Batch operations**: Use built-in batching for read receipts
6. **Test thoroughly**: Verify real-time updates work across multiple clients

## Testing

### Test Real-time Updates

1. Open the application in two browser windows
2. Add a comment in one window
3. Verify it appears immediately in the other window
4. Check that read receipts update in real-time

### Test Read Receipts

1. Add comments as one user
2. Open comments modal as another user
3. Verify comments are automatically marked as read
4. Check that read receipts appear for the first user

### Performance Testing

1. Add RealtimeStatus component to monitor listeners
2. Navigate between chats and verify listeners start/stop correctly
3. Check console for any error messages
4. Monitor Firebase usage in console
