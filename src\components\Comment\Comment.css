/* Comment Item Container - Traditional comment styling */
.comment-item {
  display: flex;
  flex-direction: column;
  padding: 16px 0;
  gap: 12px;
  opacity: 0;
  animation: commentFadeIn 0.4s ease-out forwards;
  transform: translateY(10px);
  position: relative;
  border-bottom: 1px solid #f0f0f0;
}

/* Ensure text is always visible after animation */
.comment-item * {
  opacity: 1 !important;
}

.comment-item:last-child {
  border-bottom: none;
}

/* Comment Fade In Animation */
@keyframes commentFadeIn {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Comment Header */
.comment-header {
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

.comment-author {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

/* Comment Avatar */
.comment-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  border: 2px solid white;
}

.comment-avatar-image {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  object-fit: cover;
  display: block;
}

.comment-avatar-initials {
  color: white;
  font-weight: 600;
  font-size: 14px;
  line-height: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

/* Comment Author Info */
.comment-author-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
  min-width: 0;
  flex: 1;
}

.comment-author-name {
  font-weight: 600;
  font-size: 14px;
  color: #000000 !important;
  line-height: 1.2;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.comment-timestamp {
  font-size: 12px;
  color: #5a5a5a;
  font-weight: 500;
  line-height: 1.2;
  display: flex;
  align-items: center;
  gap: 8px;
}

/* Read Receipts Icon */
.read-receipts-icon {
  background: none;
  border: none;
  color: #5a5a5a;
  font-size: 12px;
  cursor: pointer;
  padding: 4px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0.8;
  transition: all 0.2s ease;
}

.read-receipts-icon:hover {
  opacity: 1;
  background-color: rgba(0, 123, 255, 0.1);
  color: #007bff;
  transform: scale(1.1);
}

/* Comment Content */
.comment-content {
  margin-left: 52px;
  margin-top: -8px;
}

.comment-text {
  margin: 0;
  font-size: 14px;
  line-height: 1.5;
  color: #000000 !important;
  font-weight: 600;
  word-wrap: break-word;
  white-space: pre-wrap;
}

/* Comment Image */
.comment-image-container {
  margin-bottom: 12px;
  border-radius: 8px;
  overflow: hidden;
  max-width: 300px;
}

.comment-image {
  width: 100%;
  height: auto;
  max-height: 200px;
  object-fit: cover;
  cursor: pointer;
  transition: opacity 0.2s ease;
}

.comment-image:hover {
  opacity: 0.9;
}

.image-error {
  padding: 12px;
  text-align: center;
  color: #6c757d;
  font-size: 12px;
  background: rgba(0, 0, 0, 0.05);
  border-radius: 8px;
}

/* Read Receipts */
.comment-read-receipts {
  margin-left: 52px;
  margin-top: 8px;
}

.read-receipts-content {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  align-items: center;
}

.read-receipt-item {
  font-size: 11px;
  color: #5a5a5a;
  font-weight: 500;
  line-height: 1.3;
  display: flex;
  align-items: center;
  gap: 4px;
  background: rgba(0, 0, 0, 0.08);
  padding: 2px 6px;
  border-radius: 10px;
  transition: all 0.2s ease;
}

.read-receipt-item:hover {
  background: rgba(0, 0, 0, 0.1);
}

.read-receipt-item.current-user {
  color: #28a745;
  font-weight: 500;
  background: rgba(40, 167, 69, 0.1);
}

.read-receipt-item.current-user:hover {
  background: rgba(40, 167, 69, 0.2);
}

.read-receipt-icon {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: currentColor;
  opacity: 0.6;
}

.read-receipt-summary {
  font-size: 10px;
  color: #5a5a5a;
  font-weight: 500;
  margin-top: 4px;
  font-style: italic;
  padding: 4px 6px;
  border-radius: 6px;
  transition: all 0.2s ease;
  user-select: none;
}

.read-receipt-summary:hover {
  background: rgba(0, 123, 255, 0.1);
  color: #007bff;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 123, 255, 0.2);
}

.read-receipt-summary.current-user-read {
  color: #28a745;
}

.read-receipt-summary.current-user-read:hover {
  background: rgba(40, 167, 69, 0.1);
  color: #1e7e34;
  box-shadow: 0 2px 4px rgba(40, 167, 69, 0.2);
}

/* Read Receipts Modal */
.read-receipts-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  animation: fadeIn 0.2s ease-out;
}

.read-receipts-modal {
  background: white;
  border-radius: 12px;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
  max-width: 400px;
  width: 90%;
  max-height: 500px;
  overflow: hidden;
  animation: slideUp 0.3s ease-out;
}

.read-receipts-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #e9ecef;
  background-color: #f8f9fa;
}

.read-receipts-modal-header h6 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #212529;
}

.close-modal-btn {
  background: none;
  border: none;
  font-size: 24px;
  color: #6c757d;
  cursor: pointer;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.close-modal-btn:hover {
  background-color: #e9ecef;
  color: #495057;
}

.read-receipts-modal-body {
  padding: 16px 20px;
  max-height: 400px;
  overflow-y: auto;
}

.read-receipts-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.read-receipt-user {
  display: flex;
  align-items: center;
  gap: 12px;
}

.read-receipt-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  font-size: 14px;
  flex-shrink: 0;
  position: relative;
  overflow: hidden;
}

.read-receipt-avatar-image {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  object-fit: cover;
  display: block;
}

.read-receipt-avatar-initials {
  color: white;
  font-weight: 600;
  font-size: 14px;
  line-height: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.read-receipt-info {
  flex: 1;
  min-width: 0;
}

.read-receipt-name {
  font-size: 14px;
  font-weight: 500;
  color: #212529;
  margin-bottom: 2px;
}

.read-receipt-time {
  font-size: 12px;
  color: #6c757d;
}

.no-read-receipts {
  text-align: center;
  color: #6c757d;
  font-style: italic;
  padding: 20px;
}

/* Modal Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Hover Effects */
.comment-item:hover {
  background-color: rgba(0, 0, 0, 0.8);
  border-radius: 8px;
  margin: 0 -12px;
  padding: 16px 12px;
  transition: all 0.2s ease;
}

/* White text on hover */
.comment-item:hover .comment-author-name {
  color: #ffffff !important;
}

.comment-item:hover .comment-text {
  color: #ffffff !important;
}

.comment-item:hover .comment-timestamp {
  color: #e0e0e0 !important;
}

.comment-item:hover .comment-avatar {
  transform: scale(1.05);
  transition: transform 0.2s ease;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

/* Responsive Design */
@media (max-width: 768px) {
  .comment-item {
    padding: 12px 0;
  }

  .comment-header {
    gap: 10px;
  }

  .comment-author {
    gap: 10px;
  }

  .comment-avatar {
    width: 36px;
    height: 36px;
  }

  .comment-avatar-initials {
    font-size: 13px;
  }

  .comment-author-name {
    font-size: 13px;
  }

  .comment-timestamp {
    font-size: 11px;
  }

  .comment-content {
    margin-left: 46px;
  }

  .comment-text {
    font-size: 13px;
    color: #000000 !important;
    font-weight: 600;
  }

  .comment-read-receipts {
    margin-left: 46px;
    margin-top: 6px;
  }

  .read-receipt-item {
    font-size: 10px;
    padding: 1px 4px;
  }

  .read-receipt-summary {
    font-size: 9px;
  }

  .comment-item:hover {
    background-color: rgba(0, 0, 0, 0.8);
    margin: 0 -6px;
    padding: 12px 6px;
  }

  .comment-image-container {
    max-width: 250px;
  }

  .comment-image {
    max-height: 150px;
  }

  .read-receipts-modal {
    width: 95%;
    max-width: 350px;
  }

  .read-receipts-modal-header {
    padding: 12px 16px;
  }

  .read-receipts-modal-header h6 {
    font-size: 14px;
  }

  .read-receipts-modal-body {
    padding: 12px 16px;
  }

  .read-receipt-avatar {
    width: 36px;
    height: 36px;
    font-size: 13px;
  }

  .read-receipt-name {
    font-size: 13px;
  }

  .read-receipt-time {
    font-size: 11px;
  }
}

@media (max-width: 480px) {
  .comment-item {
    padding: 10px 0;
  }

  .comment-header {
    gap: 8px;
  }

  .comment-author {
    gap: 8px;
  }

  .comment-avatar {
    width: 32px;
    height: 32px;
  }

  .comment-avatar-initials {
    font-size: 12px;
  }

  .comment-author-name {
    font-size: 12px;
  }

  .comment-timestamp {
    font-size: 10px;
  }

  .comment-content {
    margin-left: 40px;
    margin-top: -2px;
  }

  .comment-text {
    font-size: 12px;
    line-height: 1.4;
    color: #000000 !important;
    font-weight: 600;
  }

  .comment-read-receipts {
    margin-left: 40px;
    margin-top: 4px;
  }

  .read-receipt-item {
    font-size: 9px;
  }

  .comment-item:hover {
    background-color: rgba(0, 0, 0, 0.8);
    margin: 0 -4px;
    padding: 10px 4px;
  }

  .comment-image-container {
    max-width: 200px;
  }

  .comment-image {
    max-height: 120px;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .comment-item {
    border-bottom-color: #4a5568;
  }

  .comment-item:hover {
    background-color: #4a5568;
  }

  .comment-author-name {
    color: #e2e8f0;
  }

  .comment-timestamp {
    color: #a0aec0;
  }

  .comment-text {
    color: #cbd5e0;
  }

  .read-receipt-item {
    color: #a0aec0;
    background: rgba(255, 255, 255, 0.1);
  }

  .read-receipt-item.current-user {
    color: #68d391;
    background: rgba(104, 211, 145, 0.1);
  }

  .read-receipt-summary {
    color: #a0aec0;
  }

  .read-receipt-summary.current-user-read {
    color: #68d391;
  }

  .read-receipts-modal {
    background: #2d3748;
  }

  .read-receipts-modal-header {
    background-color: #4a5568;
    border-bottom-color: #4a5568;
  }

  .read-receipts-modal-header h6 {
    color: #e2e8f0;
  }

  .close-modal-btn {
    color: #a0aec0;
  }

  .close-modal-btn:hover {
    background-color: #4a5568;
    color: #e2e8f0;
  }

  .read-receipt-name {
    color: #e2e8f0;
  }

  .read-receipt-time {
    color: #a0aec0;
  }

  .no-read-receipts {
    color: #a0aec0;
  }

  .read-receipts-icon:hover {
    background-color: rgba(77, 171, 247, 0.1);
    color: #4dabf7;
  }

  .image-error {
    background: rgba(255, 255, 255, 0.1);
    color: #a0aec0;
  }
}

/* Accessibility */
.comment-item:focus-within {
  outline: 2px solid #007bff;
  outline-offset: 2px;
  border-radius: 8px;
}

.read-receipts-icon:focus,
.close-modal-btn:focus {
  outline: 2px solid #007bff;
  outline-offset: 2px;
}

.comment-image:focus {
  outline: 2px solid #007bff;
  outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .comment-avatar {
    border: 2px solid #000;
  }

  .comment-author-name {
    color: #000;
  }

  .comment-text {
    color: #000;
  }
}

/* Reduced motion support */
@media (prefers-redu: reduce) {
  .comment-item:hover .comment-avatar {
    transform: none;
  }

  .comment-item:hover {
    transition: none;
  }

  .comment-item {
    animation: none;
    opacity: 1;
    transform: none;
  }

  .read-receipts-modal-overlay,
  .read-receipts-modal {
    animation: none;
  }

  .read-receipts-icon,
  .close-modal-btn {
    transition: none;
  }
}
