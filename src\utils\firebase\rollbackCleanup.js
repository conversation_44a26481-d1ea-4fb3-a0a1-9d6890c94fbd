/**
 * Firebase Rollback and Cleanup Utilities
 *
 * This module provides utilities for rolling back migrations from chat ID-based
 * collection paths back to sender ID-based paths, and for cleaning up old
 * collection paths after successful migration.
 */

import {
    collection,
    doc,
    getDocs,
    deleteDoc,
    writeBatch,
    query,
    orderBy,
    getDoc,
    setDoc
} from 'firebase/firestore';
import { db } from '../firebase.config';
import {
    getDualWritePaths,
    determineChatType,
    requiresMigration
} from '../../services/firebase/collectionPaths';
import {
    logMigrationOperation,
    withPerformanceMonitoring,
    LOG_LEVELS,
    OPERATION_TYPES
} from './migrationMonitoring';
import { verifyMigrationIntegrity } from './dataMigration';

/**
 * Rollback result object structure
 * @typedef {Object} RollbackResult
 * @property {boolean} success - Whether the rollback was successful
 * @property {number} rolledBackCount - Number of messages rolled back
 * @property {number} skippedCount - Number of messages skipped
 * @property {number} errorCount - Number of messages that failed to rollback
 * @property {Array} errors - Array of error objects
 * @property {string} fromPath - Source collection path (chat ID based)
 * @property {string} toPath - Destination collection path (sender ID based)
 */

/**
 * Cleanup result object structure
 * @typedef {Object} CleanupResult
 * @property {boolean} success - Whether the cleanup was successful
 * @property {number} deletedCount - Number of messages deleted
 * @property {number} errorCount - Number of messages that failed to delete
 * @property {Array} errors - Array of error objects
 * @property {string} cleanedPath - Collection path that was cleaned
 */

/**
 * Rollback messages from chat ID-based structure back to sender ID-based structure
 * @param {Object} selectedChat - The selected chat object
 * @param {Object} options - Rollback options
 * @param {boolean} options.dryRun - If true, only simulate the rollback without making changes
 * @param {boolean} options.deleteSource - If true, delete chat ID-based messages after rollback
 * @param {number} options.batchSize - Number of documents to process in each batch
 * @param {boolean} options.verifyIntegrity - If true, verify data integrity before rollback
 * @returns {Promise<RollbackResult>} - Rollback result
 */
export const rollbackToChatIdStructure = withPerformanceMonitoring(
    'rollbackToChatIdStructure',
    async (selectedChat, options = {}) => {
        const {
            dryRun = false,
            deleteSource = false,
            batchSize = 50,
            verifyIntegrity = true
        } = options;

        const result = {
            success: false,
            rolledBackCount: 0,
            skippedCount: 0,
            errorCount: 0,
            errors: [],
            fromPath: null,
            toPath: null
        };

        try {
            // Validate input
            if (!selectedChat) {
                throw new Error('selectedChat is required');
            }

            // Determine chat type and check if rollback is applicable
            const chatType = determineChatType(selectedChat);
            if (!chatType) {
                throw new Error('Unable to determine chat type');
            }

            if (!requiresMigration(chatType)) {
                result.success = true;
                result.skippedCount = 0;
                console.log(`Chat type ${chatType} does not require rollback`);
                return result;
            }

            // Get dual write paths (reversed for rollback)
            const dualWritePaths = getDualWritePaths(selectedChat, chatType);
            if (!dualWritePaths.legacy.messages || !dualWritePaths.current.messages) {
                throw new Error('Unable to determine source and destination paths');
            }

            // For rollback: current (chat ID) becomes source, legacy (sender ID) becomes destination
            result.fromPath = dualWritePaths.current.messages;
            result.toPath = dualWritePaths.legacy.messages;

            await logMigrationOperation(
                OPERATION_TYPES.ROLLBACK,
                LOG_LEVELS.INFO,
                `Starting rollback for ${chatType} chat`,
                {
                    chatId: dualWritePaths.chatId,
                    fromPath: result.fromPath,
                    toPath: result.toPath,
                    dryRun,
                    deleteSource,
                    phase: 'start'
                },
                dualWritePaths.chatId,
                chatType
            );

            console.log(`Starting rollback for ${chatType} chat:`, {
                chatId: dualWritePaths.chatId,
                fromPath: result.fromPath,
                toPath: result.toPath,
                dryRun,
                deleteSource
            });

            // Verify data integrity before rollback if requested
            if (verifyIntegrity && !dryRun) {
                console.log('Verifying data integrity before rollback...');
                const integrityResult = await verifyMigrationIntegrity(selectedChat);
                if (!integrityResult.success) {
                    throw new Error(`Data integrity verification failed: ${integrityResult.missingInDestination.length} missing messages, ${integrityResult.dataIntegrityIssues.length} integrity issues`);
                }
                console.log('Data integrity verification passed');
            }

            // Check if source and destination are the same (no rollback needed)
            if (result.fromPath === result.toPath) {
                result.success = true;
                result.skippedCount = 0;
                console.log('Source and destination paths are identical, no rollback needed');
                return result;
            }

            // Get source messages (from chat ID structure)
            const [sourceMainCollection, sourceChatId, sourceSubCollection] = result.fromPath.split('/');
            const sourceCollectionRef = collection(db, sourceMainCollection, sourceChatId, sourceSubCollection);
            const sourceQuery = query(sourceCollectionRef, orderBy('created_time', 'asc'));
            const sourceSnapshot = await getDocs(sourceQuery);

            if (sourceSnapshot.empty) {
                result.success = true;
                result.skippedCount = 0;
                console.log('No messages found in source collection for rollback');
                return result;
            }

            console.log(`Found ${sourceSnapshot.docs.length} messages to rollback`);

            // Check if destination already has messages (to avoid duplicates)
            const [destMainCollection, destChatId, destSubCollection] = result.toPath.split('/');
            const destCollectionRef = collection(db, destMainCollection, destChatId, destSubCollection);
            const destSnapshot = await getDocs(destCollectionRef);
            const existingMessageIds = new Set(destSnapshot.docs.map(doc => doc.id));

            console.log(`Destination collection has ${destSnapshot.docs.length} existing messages`);

            if (dryRun) {
                // Dry run: just count what would be rolled back
                const messagesToRollback = sourceSnapshot.docs.filter(doc => !existingMessageIds.has(doc.id));
                result.rolledBackCount = messagesToRollback.length;
                result.skippedCount = sourceSnapshot.docs.length - messagesToRollback.length;
                result.success = true;
                console.log(`Dry run complete: ${result.rolledBackCount} would be rolled back, ${result.skippedCount} would be skipped`);
                return result;
            }

            // Process messages in batches
            const sourceMessages = sourceSnapshot.docs;
            const batches = [];

            for (let i = 0; i < sourceMessages.length; i += batchSize) {
                batches.push(sourceMessages.slice(i, i + batchSize));
            }

            console.log(`Processing ${batches.length} batches of up to ${batchSize} messages each`);

            // Ensure destination parent document exists
            await ensureParentDocumentExists(destMainCollection, destChatId, chatType);

            // Process each batch
            for (let batchIndex = 0; batchIndex < batches.length; batchIndex++) {
                const batch = batches[batchIndex];
                console.log(`Processing rollback batch ${batchIndex + 1}/${batches.length} (${batch.length} messages)`);

                const batchResult = await rollbackBatch(
                    batch,
                    destMainCollection,
                    destChatId,
                    destSubCollection,
                    existingMessageIds,
                    deleteSource ? { sourceMainCollection, sourceChatId, sourceSubCollection } : null
                );

                result.rolledBackCount += batchResult.rolledBackCount;
                result.skippedCount += batchResult.skippedCount;
                result.errorCount += batchResult.errorCount;
                result.errors.push(...batchResult.errors);
            }

            result.success = result.errorCount === 0;

            await logMigrationOperation(
                OPERATION_TYPES.ROLLBACK,
                result.success ? LOG_LEVELS.SUCCESS : LOG_LEVELS.ERROR,
                `Rollback ${result.success ? 'completed successfully' : 'completed with errors'}`,
                {
                    rolledBackCount: result.rolledBackCount,
                    skippedCount: result.skippedCount,
                    errorCount: result.errorCount,
                    phase: 'complete'
                },
                dualWritePaths.chatId,
                chatType
            );

            console.log(`Rollback complete:`, {
                success: result.success,
                rolledBackCount: result.rolledBackCount,
                skippedCount: result.skippedCount,
                errorCount: result.errorCount
            });

            return result;

        } catch (error) {
            console.error('Rollback failed:', error);
            result.errors.push({
                type: 'rollback_error',
                message: error.message,
                error: error
            });
            return result;
        }
    },
    { chatId: selectedChat?.id, chatType: determineChatType(selectedChat) }
);

/**
 * Rollback a batch of messages
 * @private
 */
const rollbackBatch = async (
    messageDocs,
    destMainCollection,
    destChatId,
    destSubCollection,
    existingMessageIds,
    deleteSourceInfo = null
) => {
    const batchResult = {
        rolledBackCount: 0,
        skippedCount: 0,
        errorCount: 0,
        errors: []
    };

    const batch = writeBatch(db);
    const deleteOperations = [];

    for (const messageDoc of messageDocs) {
        try {
            // Skip if message already exists in destination
            if (existingMessageIds.has(messageDoc.id)) {
                batchResult.skippedCount++;
                continue;
            }

            // Add message to destination (sender ID structure)
            const destDocRef = doc(db, destMainCollection, destChatId, destSubCollection, messageDoc.id);
            batch.set(destDocRef, messageDoc.data());

            // Prepare delete operation if requested
            if (deleteSourceInfo) {
                deleteOperations.push({
                    ref: doc(db, deleteSourceInfo.sourceMainCollection, deleteSourceInfo.sourceChatId, deleteSourceInfo.sourceSubCollection, messageDoc.id),
                    id: messageDoc.id
                });
            }

            batchResult.rolledBackCount++;

        } catch (error) {
            console.error(`Error preparing rollback for message ${messageDoc.id}:`, error);
            batchResult.errorCount++;
            batchResult.errors.push({
                type: 'message_rollback_error',
                messageId: messageDoc.id,
                message: error.message,
                error: error
            });
        }
    }

    // Execute the batch write
    if (batchResult.rolledBackCount > 0) {
        try {
            await batch.commit();
            console.log(`Successfully rolled back batch of ${batchResult.rolledBackCount} messages`);

            // Delete source messages if requested and rollback was successful
            if (deleteSourceInfo && deleteOperations.length > 0) {
                try {
                    const deleteBatch = writeBatch(db);
                    deleteOperations.forEach(({ ref }) => {
                        deleteBatch.delete(ref);
                    });
                    await deleteBatch.commit();
                    console.log(`Successfully deleted ${deleteOperations.length} source messages during rollback`);
                } catch (deleteError) {
                    console.error('Error deleting source messages during rollback:', deleteError);
                    batchResult.errors.push({
                        type: 'source_deletion_error',
                        message: deleteError.message,
                        error: deleteError,
                        affectedMessages: deleteOperations.map(op => op.id)
                    });
                }
            }

        } catch (error) {
            console.error('Error executing rollback batch:', error);
            batchResult.errorCount += batchResult.rolledBackCount;
            batchResult.rolledBackCount = 0;
            batchResult.errors.push({
                type: 'batch_write_error',
                message: error.message,
                error: error
            });
        }
    }

    return batchResult;
};

/**
 * Clean up old sender ID-based collection paths after successful migration
 * @param {Object} selectedChat - The selected chat object
 * @param {Object} options - Cleanup options
 * @param {boolean} options.dryRun - If true, only simulate the cleanup without making changes
 * @param {number} options.batchSize - Number of documents to delete in each batch
 * @param {boolean} options.verifyMigration - If true, verify migration before cleanup
 * @returns {Promise<CleanupResult>} - Cleanup result
 */
export const cleanupOldSenderIdPaths = withPerformanceMonitoring(
    'cleanupOldSenderIdPaths',
    async (selectedChat, options = {}) => {
        const {
            dryRun = false,
            batchSize = 50,
            verifyMigration = true
        } = options;

        const result = {
            success: false,
            deletedCount: 0,
            errorCount: 0,
            errors: [],
            cleanedPath: null
        };

        try {
            // Validate input
            if (!selectedChat) {
                throw new Error('selectedChat is required');
            }

            // Determine chat type and check if cleanup is applicable
            const chatType = determineChatType(selectedChat);
            if (!chatType) {
                throw new Error('Unable to determine chat type');
            }

            if (!requiresMigration(chatType)) {
                result.success = true;
                result.deletedCount = 0;
                console.log(`Chat type ${chatType} does not require cleanup`);
                return result;
            }

            // Get dual write paths
            const dualWritePaths = getDualWritePaths(selectedChat, chatType);
            if (!dualWritePaths.legacy.messages || !dualWritePaths.current.messages) {
                throw new Error('Unable to determine collection paths');
            }

            result.cleanedPath = dualWritePaths.legacy.messages;

            await logMigrationOperation(
                OPERATION_TYPES.CLEANUP,
                LOG_LEVELS.INFO,
                `Starting cleanup for ${chatType} chat`,
                {
                    chatId: dualWritePaths.chatId,
                    cleanedPath: result.cleanedPath,
                    dryRun,
                    phase: 'start'
                },
                dualWritePaths.chatId,
                chatType
            );

            console.log(`Starting cleanup for ${chatType} chat:`, {
                chatId: dualWritePaths.chatId,
                cleanedPath: result.cleanedPath,
                dryRun
            });

            // Verify migration is complete before cleanup if requested
            if (verifyMigration && !dryRun) {
                console.log('Verifying migration completion before cleanup...');
                const integrityResult = await verifyMigrationIntegrity(selectedChat);
                if (!integrityResult.success) {
                    throw new Error(`Migration verification failed: ${integrityResult.missingInDestination.length} missing messages, ${integrityResult.dataIntegrityIssues.length} integrity issues`);
                }
                console.log('Migration verification passed, proceeding with cleanup');
            }

            // Get messages to clean up (from old sender ID structure)
            const [mainCollection, chatId, subCollection] = result.cleanedPath.split('/');
            const collectionRef = collection(db, mainCollection, chatId, subCollection);
            const snapshot = await getDocs(collectionRef);

            if (snapshot.empty) {
                result.success = true;
                result.deletedCount = 0;
                console.log('No messages found in old collection path for cleanup');
                return result;
            }

            console.log(`Found ${snapshot.docs.length} messages to clean up`);

            if (dryRun) {
                // Dry run: just count what would be deleted
                result.deletedCount = snapshot.docs.length;
                result.success = true;
                console.log(`Dry run complete: ${result.deletedCount} messages would be deleted`);
                return result;
            }

            // Process messages in batches for deletion
            const messageDocs = snapshot.docs;
            const batches = [];

            for (let i = 0; i < messageDocs.length; i += batchSize) {
                batches.push(messageDocs.slice(i, i + batchSize));
            }

            console.log(`Processing ${batches.length} cleanup batches of up to ${batchSize} messages each`);

            // Process each batch
            for (let batchIndex = 0; batchIndex < batches.length; batchIndex++) {
                const batch = batches[batchIndex];
                console.log(`Processing cleanup batch ${batchIndex + 1}/${batches.length} (${batch.length} messages)`);

                const batchResult = await cleanupBatch(batch, mainCollection, chatId, subCollection);

                result.deletedCount += batchResult.deletedCount;
                result.errorCount += batchResult.errorCount;
                result.errors.push(...batchResult.errors);
            }

            result.success = result.errorCount === 0;

            await logMigrationOperation(
                OPERATION_TYPES.CLEANUP,
                result.success ? LOG_LEVELS.SUCCESS : LOG_LEVELS.ERROR,
                `Cleanup ${result.success ? 'completed successfully' : 'completed with errors'}`,
                {
                    deletedCount: result.deletedCount,
                    errorCount: result.errorCount,
                    phase: 'complete'
                },
                dualWritePaths.chatId,
                chatType
            );

            console.log(`Cleanup complete:`, {
                success: result.success,
                deletedCount: result.deletedCount,
                errorCount: result.errorCount
            });

            return result;

        } catch (error) {
            console.error('Cleanup failed:', error);
            result.errors.push({
                type: 'cleanup_error',
                message: error.message,
                error: error
            });
            return result;
        }
    },
    { chatId: selectedChat?.id, chatType: determineChatType(selectedChat) }
);

/**
 * Clean up a batch of messages
 * @private
 */
const cleanupBatch = async (messageDocs, mainCollection, chatId, subCollection) => {
    const batchResult = {
        deletedCount: 0,
        errorCount: 0,
        errors: []
    };

    const batch = writeBatch(db);

    for (const messageDoc of messageDocs) {
        try {
            const docRef = doc(db, mainCollection, chatId, subCollection, messageDoc.id);
            batch.delete(docRef);
            batchResult.deletedCount++;
        } catch (error) {
            console.error(`Error preparing cleanup for message ${messageDoc.id}:`, error);
            batchResult.errorCount++;
            batchResult.errors.push({
                type: 'message_cleanup_error',
                messageId: messageDoc.id,
                message: error.message,
                error: error
            });
        }
    }

    // Execute the batch delete
    if (batchResult.deletedCount > 0) {
        try {
            await batch.commit();
            console.log(`Successfully cleaned up batch of ${batchResult.deletedCount} messages`);
        } catch (error) {
            console.error('Error executing cleanup batch:', error);
            batchResult.errorCount += batchResult.deletedCount;
            batchResult.deletedCount = 0;
            batchResult.errors.push({
                type: 'batch_delete_error',
                message: error.message,
                error: error
            });
        }
    }

    return batchResult;
};

/**
 * Ensure parent document exists for the destination collection
 * @private
 */
const ensureParentDocumentExists = async (mainCollection, chatId, chatType) => {
    try {
        const parentDocRef = doc(db, mainCollection, chatId);
        const parentDoc = await getDoc(parentDocRef);

        if (!parentDoc.exists()) {
            await setDoc(parentDocRef, {
                chatId,
                chatType,
                createdAt: new Date().toISOString(),
                lastActivity: new Date().toISOString(),
                rolledBackAt: new Date().toISOString()
            });
            console.log(`Created parent document for rollback: ${mainCollection}/${chatId}`);
        }
    } catch (error) {
        console.error('Error ensuring parent document exists for rollback:', error);
        // Don't throw - this is not critical for the rollback
    }
};

/**
 * Get comprehensive status of migration, rollback, and cleanup operations
 * @param {Object} selectedChat - The selected chat object
 * @returns {Promise<Object>} - Comprehensive status
 */
export const getComprehensiveStatus = async (selectedChat) => {
    try {
        const chatType = determineChatType(selectedChat);

        if (!requiresMigration(chatType)) {
            return {
                requiresOperations: false,
                chatType,
                status: 'not_required'
            };
        }

        const dualWritePaths = getDualWritePaths(selectedChat, chatType);

        // Check both collection paths
        const [legacyMainCollection, legacyChatId, legacySubCollection] = dualWritePaths.legacy.messages.split('/');
        const legacyCollectionRef = collection(db, legacyMainCollection, legacyChatId, legacySubCollection);
        const legacySnapshot = await getDocs(legacyCollectionRef);
        const legacyCount = legacySnapshot.docs.length;

        const [currentMainCollection, currentChatId, currentSubCollection] = dualWritePaths.current.messages.split('/');
        const currentCollectionRef = collection(db, currentMainCollection, currentChatId, currentSubCollection);
        const currentSnapshot = await getDocs(currentCollectionRef);
        const currentCount = currentSnapshot.docs.length;

        let status;
        let recommendedAction;

        if (legacyCount === 0 && currentCount === 0) {
            status = 'no_data';
            recommendedAction = 'none';
        } else if (legacyCount > 0 && currentCount === 0) {
            status = 'not_migrated';
            recommendedAction = 'migrate';
        } else if (legacyCount === 0 && currentCount > 0) {
            status = 'fully_migrated_and_cleaned';
            recommendedAction = 'none';
        } else if (legacyCount > 0 && currentCount > 0) {
            status = 'migrated_needs_cleanup';
            recommendedAction = 'cleanup';
        }

        return {
            requiresOperations: true,
            chatType,
            status,
            recommendedAction,
            legacyCount,
            currentCount,
            legacyPath: dualWritePaths.legacy.messages,
            currentPath: dualWritePaths.current.messages,
            canRollback: currentCount > 0,
            canCleanup: legacyCount > 0 && currentCount > 0
        };

    } catch (error) {
        console.error('Error getting comprehensive status:', error);
        return {
            requiresOperations: false,
            error: error.message
        };
    }
};
