/**
 * Test utility to verify the unread comments fix
 * This demonstrates that user ID comparison is working correctly
 */

import { store } from '../redux/store';
import {
    updateCommentsRealtime,
    updateUnreadCommentCounts,
    selectChatUnreadComments,
    selectCommentsUnreadCount
} from '../redux/features/metaBusinessChatSlice';

/**
 * Test the unread comments functionality with proper user ID comparison
 */
export const testUnreadCommentsFix = () => {
    console.log('🧪 [TEST] Starting unread comments fix test...');

    // Mock current user (from your data)
    const currentUserId = "1"; // admin user ID as string

    // Mock comments data (based on your provided data structure)
    const mockComments = [
        {
            id: "comment-1",
            text: "Test comment 1",
            author: {
                id: "409", // Different user
                name: "abdelrahman moderator",
                email: "<EMAIL>"
            },
            createdAt: "2025-07-31T08:53:31.947Z",
            readBy: [
                {
                    readAt: "2025-07-31T08:53:31.947Z",
                    userId: "1", // Current user has read this
                    userName: "admin7",
                    userPhoto: "images/clients/1729597373859.jpeg"
                },
                {
                    readAt: "2025-07-31T08:53:45.814Z",
                    userId: "409",
                    userName: "abdelrahman moderator",
                    userPhoto: null
                }
            ]
        },
        {
            id: "comment-2",
            text: "Test comment 2",
            author: {
                id: "316", // Different user
                name: "Moderator",
                email: "<EMAIL>"
            },
            createdAt: "2025-07-31T11:16:28.668Z",
            readBy: [
                {
                    readAt: "2025-07-31T11:16:28.668Z",
                    userId: "316",
                    userName: "Moderator",
                    userPhoto: null
                }
                // Current user (ID "1") has NOT read this comment
            ]
        },
        {
            id: "comment-3",
            text: "Test comment 3",
            author: {
                id: "1", // Current user's own comment
                name: "admin7",
                email: "<EMAIL>"
            },
            createdAt: "2025-07-31T12:00:00.000Z",
            readBy: []
        }
    ];

    console.log('📊 [TEST] Mock data prepared:', {
        currentUserId,
        totalComments: mockComments.length,
        commentsFromOthers: mockComments.filter(c => c.author.id !== currentUserId).length
    });

    // Test 1: Update comments with proper currentUserId
    console.log('🔄 [TEST] Test 1: Updating comments with currentUserId...');
    store.dispatch(updateCommentsRealtime({
        comments: mockComments,
        currentUserId: currentUserId
    }));

    // Test 2: Update unread counts
    console.log('🔄 [TEST] Test 2: Updating unread counts...');
    store.dispatch(updateUnreadCommentCounts({
        currentUserId: currentUserId
    }));

    // Test 3: Check results
    const state = store.getState().metaBusinessSuite;
    const unreadCount = state.commentsModal.unreadCount;
    const chatUnreadComments = state.chatUnreadComments;

    console.log('📈 [TEST] Results:', {
        totalCommentsInState: state.comments.length,
        unreadCount: unreadCount,
        chatUnreadComments: chatUnreadComments
    });

    // Expected results:
    // - comment-1: Read by current user (userId "1") -> should NOT be counted as unread
    // - comment-2: NOT read by current user -> should be counted as unread
    // - comment-3: Current user's own comment -> should NOT be counted as unread
    // Expected unread count: 1

    const expectedUnreadCount = 1;
    const testPassed = unreadCount === expectedUnreadCount;

    console.log('✅ [TEST] Test Results:', {
        expected: expectedUnreadCount,
        actual: unreadCount,
        passed: testPassed ? '✅ PASSED' : '❌ FAILED'
    });

    if (testPassed) {
        console.log('🎉 [TEST] Unread comments fix is working correctly!');
        console.log('🔍 [TEST] User ID comparison is properly handling string vs number comparison');
    } else {
        console.error('❌ [TEST] Unread comments fix is NOT working correctly');
        console.error('🐛 [TEST] There may still be issues with user ID comparison');
    }

    return {
        passed: testPassed,
        expected: expectedUnreadCount,
        actual: unreadCount,
        details: {
            totalComments: state.comments.length,
            chatUnreadComments
        }
    };
};

/**
 * Test with edge cases to ensure robustness
 */
export const testUnreadCommentsEdgeCases = () => {
    console.log('🧪 [TEST] Testing edge cases...');

    const currentUserId = "1";

    // Edge case 1: Comments with mixed userId types (string vs number)
    const edgeCaseComments = [
        {
            id: "edge-1",
            text: "Edge case comment",
            author: { id: "999", name: "Test User" },
            createdAt: new Date().toISOString(),
            readBy: [
                { userId: 1, userName: "admin7" }, // Number instead of string
                { userId: "316", userName: "Moderator" }
            ]
        },
        {
            id: "edge-2",
            text: "Another edge case",
            author: { id: "888", name: "Another User" },
            createdAt: new Date().toISOString(),
            readBy: [
                { userId: "1", userName: "admin7" }, // String (correct)
                { userId: "777", userName: "Other User" }
            ]
        }
    ];

    store.dispatch(updateCommentsRealtime({
        comments: edgeCaseComments,
        currentUserId: currentUserId
    }));

    store.dispatch(updateUnreadCommentCounts({
        currentUserId: currentUserId
    }));

    const state = store.getState().metaBusinessSuite;
    const unreadCount = state.commentsModal.unreadCount;

    // Expected: edge-1 should be unread (userId 1 != "1"), edge-2 should be read (userId "1" === "1")
    const expectedUnreadCount = 1;
    const testPassed = unreadCount === expectedUnreadCount;

    console.log('🔍 [TEST] Edge case results:', {
        expected: expectedUnreadCount,
        actual: unreadCount,
        passed: testPassed ? '✅ PASSED' : '❌ FAILED'
    });

    return {
        passed: testPassed,
        expected: expectedUnreadCount,
        actual: unreadCount
    };
};

// Export for use in development/debugging
export default {
    testUnreadCommentsFix,
    testUnreadCommentsEdgeCases
};
