import { useSelector } from "react-redux";
import {
  selectSelectedChat,
  selectMessages,
  selectActiveFilter,
  selectSelectedPhone,
  selectSelectedWhatsappChat,
  formatDate,
} from "../../redux/features/metaBusinessChatSlice";
import { useState, useEffect } from "react";
import { FaUserCircle } from "react-icons/fa";
import CenteredModal from "../../components/Shared/modals/CenteredModal/CenteredModal";
import { FaPlay, FaXmark } from "react-icons/fa6";
import { getMediaType } from "../../utils/get-media-type";
import { Spinner } from "react-bootstrap";

const ImgVideoModalPreview = ({ message, handleClose }) => {
  const mediaType = getMediaType(message);
  const responsiveMediaStyles = {
    maxWidth: "80vw",
    maxHeight: "80vh",
    width: "100%",
    height: "auto",
    objectFit: "contain",
  };

  // Get the correct content to display based on message type and platform
  const getModalContent = () => {
    // For image or video messages, check all possible locations for the URL
    if (
      message.type === "image" ||
      message.type === "video" ||
      message.type === "sticker"
    ) {
      return message.url || message.message;
    }
    // For other types, use the message field
    return message.message;
  };

  const content = getModalContent();

  return (
    <div className="img-video-modal-preview">
      <FaXmark
        color="red"
        size={30}
        onClick={handleClose}
        className="modal-close-icon rounded-circle bg-white p-1"
      />
      {mediaType === "image" ? (
        content ? (
          <img
            src={content}
            alt="Media"
            style={responsiveMediaStyles}
            onError={(e) => {
              console.error("Modal image failed to load:", e.target.src);
              e.target.onerror = null;
              e.target.style.display = "none";

              // Create error message
              const errorDiv = document.createElement("div");
              errorDiv.textContent = "Image could not be loaded";
              errorDiv.style.padding = "20px";
              errorDiv.style.backgroundColor = "#f8d7da";
              errorDiv.style.color = "#721c24";
              errorDiv.style.borderRadius = "5px";
              errorDiv.style.margin = "20px";
              errorDiv.style.textAlign = "center";

              e.target.parentNode.appendChild(errorDiv);
            }}
          />
        ) : (
          <div
            style={{
              padding: "20px",
              backgroundColor: "#f8d7da",
              color: "#721c24",
              borderRadius: "5px",
              margin: "20px",
              textAlign: "center",
            }}
          >
            Image not available
          </div>
        )
      ) : mediaType === "video" ? (
        <video controls style={responsiveMediaStyles}>
          <source src={content} type="video/mp4" />
          Your browser does not support the video tag.
        </video>
      ) : null}
    </div>
  );
};

const Message = ({ message, userAvatar, pageAvatar, isSending = false }) => {
  const [showTime, setShowTime] = useState(false);
  const [showCenteredModal, setShowCenteredModal] = useState(false);
  const [imageError, setImageError] = useState(false);

  const selectedChat = useSelector(selectSelectedChat);
  const messages = useSelector(selectMessages);
  const activeFilter = useSelector(selectActiveFilter);
  const selectedPhone = useSelector(selectSelectedPhone);
  const selectedWhatsappChat = useSelector(selectSelectedWhatsappChat);

  const isWhatsChat = activeFilter === "whatsapp";
  const userId =
    activeFilter === "instagram"
      ? selectedChat?.participants?.data[1]?.id?.toString()
      : selectedChat?.participants?.data[0]?.id?.toString();
  const pageId =
    activeFilter === "instagram"
      ? selectedChat?.participants?.data[0]?.id?.toString()
      : selectedChat?.participants?.data[1]?.id?.toString();
  // Get the latest message sent by the user (not the page)
  // For Messenger/Instagram: user messages have from.id !== pageId
  // For WhatsApp: user messages have sender !== selectedPhone.display_phone_number
  const latestUserMessage = messages
    .filter((message) => {
      if (isWhatsChat) {
        // For WhatsApp: user messages are those NOT sent by the selected phone
        const normalizedSelectedPhone = selectedPhone?.display_phone_number
          ?.trim()
          ?.replace(/^\+|\s+/g, "");
        const normalizedMessageSender = message?.sender
          ?.toString()
          ?.trim()
          ?.replace(/^\+|\s+/g, "");
        return normalizedMessageSender !== normalizedSelectedPhone;
      } else {
        // For Messenger/Instagram: user messages are those NOT sent by the page
        return message?.from?.id?.toString() !== pageId?.toString();
      }
    })
    .slice(-1)[0];
  const latestPageMessage = messages
    .filter((message) => message?.from?.id?.toString() === pageId?.toString())
    .slice(-1)[0];
  const isLatestPageMessage =
    message?.id?.toString() === latestPageMessage?.id?.toString() ||
    message?.message_id?.toString() === latestPageMessage?.id?.toString();
  const isLatestUserMessage =
    message?.id?.toString() === latestUserMessage?.id?.toString() ||
    message?.message_id?.toString() === latestUserMessage?.id?.toString();

  // Determine media type based on message type and activeFilter
  const mediaType = isWhatsChat
    ? message.type // For WhatsApp, use the type directly
    : getMediaType(message); // For other platforms, use the helper function

  const toggleTime = () => {
    setShowTime(!showTime);
  };

  // Get the correct message content based on the platform and message type
  const getMessageContent = () => {
    if (isWhatsChat) {
      // For WhatsApp messages
      if (
        message.type === "image" ||
        message.type === "video" ||
        message.type === "audio" ||
        message.type === "sticker"
      ) {
        // Check all possible locations for the media URL
        const mediaUrl = message.url || message.message;
        return mediaUrl;
      } else {
        return message.message; // Use message for text
      }
    } else {
      // For other platforms (Instagram, Messenger)
      return (
        message.message ||
        message.text ||
        (message?.attachments && message.attachments.data?.[0]?.file_url) ||
        ""
      );
    }
  };

  const messageContent = getMessageContent();

  // Determine if this message was sent by the page (you) or by the user
  const isPageMessage = isWhatsChat
    ? message?.sender === selectedPhone?.display_phone_number?.trim()?.replace(/^\+|\s+/g, "")
    : message?.from?.id?.toString() === pageId?.toString() ||
      message?.sender?.toString() === pageId?.toString();

  // User message is the opposite of page message
  const isUserMessage = !isPageMessage;

  const classNames = isPageMessage ? "right" : "";

  const PageImage = () => {
    return pageAvatar ? (
      <img
        src={pageAvatar}
        alt="page avatar"
        className="rounded-circle ms-2 mb-3 avatar-drop-in"
        width={30}
        height={30}
      />
    ) : (
      <FaUserCircle
        className="rounded-circle bg-white ms-2 mb-2 avatar-drop-in"
        color="gray"
        size={30}
      />
    );
  };

  // Function to validate image URL
  const validateImageUrl = async (url) => {
    if (!url) return false;
    
    // Skip validation for blob URLs (they're always valid)
    if (url.startsWith('blob:')) {
      return true;
    }
    
    try {
      const response = await fetch(url, { method: 'HEAD' });
      return response.ok;
    } catch (error) {
      console.error("Error validating image URL:", url, error);
      return false;
    }
  };

  // Validate image URL when component mounts
  useEffect(() => {
    if (message.type === "image" && message.message) {
      validateImageUrl(message.message).then(isValid => {
        if (!isValid) {
          setImageError(true);
          console.warn("Image URL is not accessible:", message.message);
        }
      });
    }
  }, [message.message, message.type]);

  return (
    <div
      className={`d-flex ${
        isPageMessage ? "justify-content-end" : "justify-content-start"
      } align-items-end`}
    >
      {/* Only show user avatar if the message is not in sending state and is the latest user message */}
      {isUserMessage &&
        isLatestUserMessage &&
        !isSending && // Add this condition to hide avatar while sending
        (() => {
          console.log(`[AVATAR_DEBUG] Showing user avatar for message ${message.id}`);
          return userAvatar === "dummy" ? (
            <FaUserCircle
              className="rounded-circle bg-white me-2 mb-2 avatar-drop-in"
              color="gray"
              size={30}
            />
          ) : (
            <img
              src={userAvatar}
              alt="user avatar"
              className="chat-bubble__left rounded-circle me-2 mb-3 avatar-drop-in"
              width={30}
              height={30}
            />
          );
        })()}
      <div
        className={`chat-bubble d-flex flex-column ${classNames} relative`}
        onClick={toggleTime}
      >
        <div className="chat-bubble__right">
          <div className="user-message mb-1">
            {message.type === "sticker" ? (
              <div className="position-relative">
                <img
                  onClick={() => {
                    setShowCenteredModal(true);
                    toggleTime();
                  }}
                  src={messageContent}
                  alt="Sticker"
                  className="sticker-img"
                  style={{
                    maxWidth: "150px",
                    maxHeight: "150px",
                    opacity: isSending ? 0.6 : 1,
                  }}
                  onError={(e) => {
                    console.error("Sticker failed to load:", e.target.src);
                    e.target.onerror = null;
                    e.target.classList.add("image-error");
                    const parent = e.target.parentNode;
                    const errorDiv = document.createElement("div");
                    errorDiv.className = "image-error-text";
                    errorDiv.textContent = "Sticker Error";
                    errorDiv.style.position = "absolute";
                    errorDiv.style.top = "50%";
                    errorDiv.style.left = "50%";
                    errorDiv.style.transform = "translate(-50%, -50%)";
                    errorDiv.style.color = "red";
                    errorDiv.style.backgroundColor = "rgba(255,255,255,0.7)";
                    errorDiv.style.padding = "5px";
                    errorDiv.style.borderRadius = "5px";
                    parent.appendChild(errorDiv);
                  }}
                />
                {isSending && (
                  <div className="position-absolute top-50 start-50 translate-middle">
                    <Spinner animation="border" variant="primary" />
                  </div>
                )}
              </div>
            ) : message.type === "text" ? (
              // Use white-space: pre-wrap to preserve spaces
              <span style={{ whiteSpace: "pre-wrap" }}>{messageContent}</span>
            ) : mediaType === "image" ? (
              <>
                <CenteredModal
                  size="lg"
                  show={showCenteredModal}
                  onHide={() => setShowCenteredModal(false)}
                  className={"message-modal"}
                >
                  <ImgVideoModalPreview
                    message={message}
                    handleClose={() => setShowCenteredModal(false)}
                  />
                </CenteredModal>
                <div className="position-relative">
                  {messageContent ? (
                    <img
                      onClick={() => {
                        setShowCenteredModal(true);
                        toggleTime();
                      }}
                      src={messageContent}
                      alt={message.type === "sticker" ? "Sticker" : "Media"}
                      width={message.type === "sticker" ? 150 : 200}
                      height={message.type === "sticker" ? 150 : 200}
                      className={`object-fit-cover rounded-5 ${
                        message.type === "sticker" ? "sticker-img" : ""
                      }`}
                      style={{ opacity: isSending ? 0.6 : 1 }}
                      onError={(e) => {
                        console.error("Image failed to load:", e.target.src);
                        e.target.onerror = null; // Prevent infinite error loop
                        setImageError(true);
                        // Instead of setting a new src, just add a class to show it's errored
                        e.target.classList.add("image-error");
                        // Add a placeholder text inside a div
                        const parent = e.target.parentNode;
                        const errorDiv = document.createElement("div");
                        errorDiv.className = "image-error-text";
                        errorDiv.textContent = "Image not available (404)";
                        errorDiv.style.position = "absolute";
                        errorDiv.style.top = "50%";
                        errorDiv.style.left = "50%";
                        errorDiv.style.transform = "translate(-50%, -50%)";
                        errorDiv.style.color = "red";
                        errorDiv.style.backgroundColor =
                          "rgba(255,255,255,0.7)";
                        errorDiv.style.padding = "5px";
                        errorDiv.style.borderRadius = "5px";
                        errorDiv.style.fontSize = "12px";
                        errorDiv.style.textAlign = "center";
                        errorDiv.style.maxWidth = "150px";
                        parent.appendChild(errorDiv);
                        
                        // Also log the full URL for debugging
                        console.log("Failed image URL:", e.target.src);
                      }}
                    />
                  ) : (
                    <div
                      className="image-placeholder"
                      style={{
                        width: "200px",
                        height: "200px",
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "center",
                        backgroundColor: "#f0f0f0",
                        borderRadius: "5px",
                      }}
                    >
                      {message.type === "sticker"
                        ? "Sticker Not Available"
                        : "Image Not Available"}
                    </div>
                  )}
                  {isSending && (
                    <div className="position-absolute top-50 start-50 translate-middle">
                      <Spinner animation="border" variant="primary" />
                    </div>
                  )}
                </div>
              </>
            ) : mediaType === "video" ? (
              <>
                <CenteredModal
                  size="lg"
                  show={showCenteredModal}
                  onHide={() => setShowCenteredModal(false)}
                  className={"message-modal"}
                >
                  <ImgVideoModalPreview
                    message={message}
                    handleClose={() => setShowCenteredModal(false)}
                  />
                </CenteredModal>
                <div
                  className="position-relative"
                  onClick={() => {
                    setShowCenteredModal(true);
                    toggleTime();
                  }}
                  style={{ cursor: "pointer" }}
                >
                  <video
                    width={200}
                    height={200}
                    className={"object-fit-cover rounded-5"}
                    style={{ opacity: isSending ? 0.6 : 1 }}
                  >
                    <source src={messageContent} type="video/mp4" />
                    Your browser does not support the video tag.
                  </video>
                  {isSending ? (
                    <div className="position-absolute top-50 start-50 translate-middle">
                      <Spinner animation="border" variant="primary" />
                    </div>
                  ) : (
                    <div className="position-absolute top-50 start-50 translate-middle">
                      <FaPlay color="white" size={30} />
                    </div>
                  )}
                </div>
              </>
            ) : mediaType === "audio" ? (
              <audio controls>
                <source src={messageContent} type="audio/mpeg" />
                Your browser does not support the audio tag.
              </audio>
            ) : (
              messageContent || null
            )}
          </div>
        </div>
        {showTime && (
          <div>{formatDate(message?.created_time || message?.created_at)}</div>
        )}
        {isSending && (
          <div className="sending-indicator text-muted small mt-1">
            Sending...
          </div>
        )}
      </div>
      {/* Only show page avatar if the message is not in sending state and is the latest page message */}
      {isPageMessage && isLatestPageMessage && !isSending && <PageImage />}
    </div>
  );
};

export default Message;
