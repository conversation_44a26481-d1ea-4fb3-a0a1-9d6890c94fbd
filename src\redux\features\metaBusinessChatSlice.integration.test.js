/**
 * Integration tests for dual write message storage in metaBusinessChatSlice
 */

import { configureStore } from '@reduxjs/toolkit';
import metaBusinessChatSlice, {
    sendMessage,
    fetchMessages,
    fetchWhatsAppMessages
} from './metaBusinessChatSlice';

// Mock Firebase Firestore
jest.mock('firebase/firestore', () => ({
    collection: jest.fn(),
    doc: jest.fn(),
    setDoc: jest.fn(),
    getDocs: jest.fn(),
    onSnapshot: jest.fn(),
    query: jest.fn(),
    orderBy: jest.fn(),
    where: jest.fn(),
    collectionGroup: jest.fn(),
    deleteDoc: jest.fn()
}));

// Mock Firebase config
jest.mock('../../utils/firebase.config', () => ({
    db: {}
}));

// Mock meta service
jest.mock('../../services/integrations/meta', () => ({
    default: {
        getMessagesForChatApi: jest.fn(),
        getMessagesForWhatsappChatApi: jest.fn(),
        sendMessageApi: jest.fn(),
        sendWhatsappMessageApi: jest.fn()
    }
}));

// Mock collection paths service
jest.mock('../../services/firebase/collectionPaths', () => ({
    getDualWritePaths: jest.fn(),
    determineChatType: jest.fn(),
    getChatIdentifier: jest.fn(),
    getCollectionPaths: jest.fn()
}));

import {
    collection,
    doc,
    setDoc,
    getDocs,
    onSnapshot,
    query,
    orderBy
} from 'firebase/firestore';

import metaService from '../../services/integrations/meta';
import {
    getDualWritePaths,
    determineChatType,
    getChatIdentifier,
    getCollectionPaths
} from '../../services/firebase/collectionPaths';

describe('MetaBusinessChatSlice Integration Tests', () => {
    let store;

    beforeEach(() => {
        store = configureStore({
            reducer: {
                metaBusinessSuite: metaBusinessChatSlice
            }
        });
        jest.clearAllMocks();
    });

    describe('Dual Write Message Storage', () => {
        const mockSelectedChat = {
            id: 'backend_chat_123',
            participants: {
                data: [{ id: 'participant_456' }]
            }
        };

        const mockSelectedPage = {
            id: 'page_123',
            page_token: 'token_123'
        };

        const mockDualWritePaths = {
            current: {
                messages: 'chats/backend_chat_123/messages',
                comments: 'chats/backend_chat_123/comments'
            },
            legacy: {
                messages: 'chats/participant_456/messages',
                comments: 'chats/backend_chat_123/comments'
            },
            chatId: 'backend_chat_123',
            requiresMigration: true
        };

        beforeEach(() => {
            // Set up initial state
            store.dispatch({
                type: 'metaBusinessSuite/setSelectedChat',
                payload: mockSelectedChat
            });
            store.dispatch({
                type: 'metaBusinessSuite/setSelectedPage',
                payload: mockSelectedPage
            });

            // Mock collection paths
            determineChatType.mockReturnValue('messenger');
            getDualWritePaths.mockReturnValue(mockDualWritePaths);
            getChatIdentifier.mockReturnValue('backend_chat_123');
        });

        test('sendMessage writes to both current and legacy paths', async () => {
            const messageText = 'Hello World';
            const mockApiResponse = {
                message_id: 'api_msg_123'
            };

            metaService.sendMessageApi.mockResolvedValue(mockApiResponse);
            setDoc.mockResolvedValue();

            const action = sendMessage({
                message: messageText,
                selectedChat: mockSelectedChat,
                selectedPage: mockSelectedPage
            });

            await store.dispatch(action);

            // Verify dual write - should write to both paths
            expect(setDoc).toHaveBeenCalledTimes(2);

            // Check that both current and legacy paths were used
            const setDocCalls = setDoc.mock.calls;
            const paths = setDocCalls.map(call => call[0]); // First argument is the doc reference

            // Verify the paths were created correctly
            expect(doc).toHaveBeenCalledWith(
                expect.anything(),
                'chats',
                'backend_chat_123',
                'messages',
                expect.any(String)
            );
            expect(doc).toHaveBeenCalledWith(
                expect.anything(),
                'chats',
                'participant_456',
                'messages',
                expect.any(String)
            );
        });

        test('sendMessage handles dual write failure gracefully', async () => {
            const messageText = 'Hello World';
            const mockApiResponse = {
                message_id: 'api_msg_123'
            };

            metaService.sendMessageApi.mockResolvedValue(mockApiResponse);

            // Mock one path to succeed, one to fail
            setDoc.mockResolvedValueOnce() // First call succeeds
                .mockRejectedValueOnce(new Error('Firebase write failed')); // Second call fails

            const action = sendMessage({
                message: messageText,
                selectedChat: mockSelectedChat,
                selectedPage: mockSelectedPage
            });

            const result = await store.dispatch(action);

            // Should still succeed if at least one write succeeds
            expect(result.type).toBe('metaBusinessSuite/sendMessage/fulfilled');
            expect(setDoc).toHaveBeenCalledTimes(2);
        });

        test('sendMessage logs dual write operations', async () => {
            const messageText = 'Hello World';
            const mockApiResponse = {
                message_id: 'api_msg_123'
            };

            // Mock console.log to capture logging
            const consoleSpy = jest.spyOn(console, 'log').mockImplementation();

            metaService.sendMessageApi.mockResolvedValue(mockApiResponse);
            setDoc.mockResolvedValue();

            const action = sendMessage({
                message: messageText,
                selectedChat: mockSelectedChat,
                selectedPage: mockSelectedPage
            });

            await store.dispatch(action);

            // Verify logging occurred
            expect(consoleSpy).toHaveBeenCalledWith(
                expect.stringContaining('[DUAL_WRITE_SUCCESS]'),
                expect.any(Object)
            );

            consoleSpy.mockRestore();
        });
    });

    describe('Fallback Read Logic', () => {
        const mockSelectedChat = {
            id: 'backend_chat_123',
            participants: {
                data: [{ id: 'participant_456' }]
            }
        };

        const mockDualWritePaths = {
            current: {
                messages: 'chats/backend_chat_123/messages'
            },
            legacy: {
                messages: 'chats/participant_456/messages'
            },
            chatId: 'backend_chat_123'
        };

        beforeEach(() => {
            determineChatType.mockReturnValue('messenger');
            getDualWritePaths.mockReturnValue(mockDualWritePaths);

            // Mock API response
            metaService.getMessagesForChatApi.mockResolvedValue([]);
        });

        test('fetchMessages reads from new path first, falls back to legacy', async () => {
            // Mock empty new path, messages in legacy path
            const mockLegacyMessages = [
                {
                    id: 'msg1',
                    data: () => ({
                        message: 'Hello',
                        sender: 'participant_456',
                        created_time: '2024-01-01T10:00:00Z'
                    })
                }
            ];

            getDocs.mockResolvedValueOnce({ docs: [] }) // New path empty
                .mockResolvedValueOnce({ docs: mockLegacyMessages }); // Legacy path has messages

            collection.mockReturnValue({});
            query.mockReturnValue({});
            orderBy.mockReturnValue({});

            // Set up state with latest messages
            store.dispatch({
                type: 'metaBusinessSuite/setLatestMessages',
                payload: [{ id: 'participant_456', sender: 'participant_456' }]
            });

            const action = fetchMessages({ thread: mockSelectedChat });
            const result = await store.dispatch(action);

            expect(result.type).toBe('metaBusinessSuite/fetchMessages/fulfilled');
            expect(result.payload.messages).toHaveLength(1);
            expect(result.payload.activeCollectionPath).toBe('chats/participant_456/messages');
        });

        test('fetchMessages uses new path when it has messages', async () => {
            const mockNewMessages = [
                {
                    id: 'msg1',
                    data: () => ({
                        message: 'Hello from new path',
                        sender: 'backend_chat_123',
                        created_time: '2024-01-01T10:00:00Z'
                    })
                }
            ];

            getDocs.mockResolvedValueOnce({ docs: mockNewMessages }); // New path has messages

            collection.mockReturnValue({});
            query.mockReturnValue({});
            orderBy.mockReturnValue({});

            // Set up state with latest messages
            store.dispatch({
                type: 'metaBusinessSuite/setLatestMessages',
                payload: [{ id: 'participant_456', sender: 'participant_456' }]
            });

            const action = fetchMessages({ thread: mockSelectedChat });
            const result = await store.dispatch(action);

            expect(result.type).toBe('metaBusinessSuite/fetchMessages/fulfilled');
            expect(result.payload.messages).toHaveLength(1);
            expect(result.payload.activeCollectionPath).toBe('chats/backend_chat_123/messages');

            // Should only call getDocs once (for new path)
            expect(getDocs).toHaveBeenCalledTimes(1);
        });

        test('fetchMessages handles read errors gracefully', async () => {
            getDocs.mockRejectedValue(new Error('Firestore read failed'));

            collection.mockReturnValue({});
            query.mockReturnValue({});
            orderBy.mockReturnValue({});

            // Set up state with latest messages
            store.dispatch({
                type: 'metaBusinessSuite/setLatestMessages',
                payload: [{ id: 'participant_456', sender: 'participant_456' }]
            });

            const action = fetchMessages({ thread: mockSelectedChat });
            const result = await store.dispatch(action);

            expect(result.type).toBe('metaBusinessSuite/fetchMessages/fulfilled');
            expect(result.payload.messages).toEqual([]);
        });
    });

    describe('WhatsApp Message Consistency', () => {
        const mockWhatsAppChat = {
            id: 'whatsapp_chat_123',
            sender_phone_number: '+1234567890'
        };

        const mockSelectedPhone = {
            id: 'phone_123',
            display_phone_number: '+0987654321'
        };

        beforeEach(() => {
            determineChatType.mockReturnValue('whatsapp');
            getChatIdentifier.mockReturnValue('1234567890');
            getCollectionPaths.mockReturnValue({
                messages: 'whatsApp/1234567890/messages',
                comments: 'whatsApp/1234567890/comments'
            });
        });

        test('fetchWhatsAppMessages uses consistent path resolution', async () => {
            const mockApiMessages = [
                {
                    id: 'msg1',
                    message: 'Hello WhatsApp',
                    from: '1234567890',
                    created_at: '2024-01-01T10:00:00Z',
                    type: 'text'
                }
            ];

            metaService.getMessagesForWhatsappChatApi.mockResolvedValue({
                data: mockApiMessages
            });

            // Mock Firebase listener setup
            const mockUnsubscribe = jest.fn();
            onSnapshot.mockImplementation((query, callback) => {
                // Simulate immediate callback with empty data
                callback({ docs: [], docChanges: () => [] });
                return mockUnsubscribe;
            });

            collection.mockReturnValue({});
            query.mockReturnValue({});
            orderBy.mockReturnValue({});

            const action = fetchWhatsAppMessages({
                thread: mockWhatsAppChat,
                selectedPhone: mockSelectedPhone
            });

            const result = await store.dispatch(action);

            expect(result.type).toBe('metaBusinessSuite/fetchWhatsAppMessages/fulfilled');
            expect(result.payload.apiMessages).toHaveLength(1);

            // Verify consistent path was used for Firebase listener
            expect(collection).toHaveBeenCalledWith(
                expect.anything(),
                'whatsApp',
                '1234567890',
                'messages'
            );
        });

        test('WhatsApp real-time listener updates messages correctly', async () => {
            const mockApiMessages = [];

            metaService.getMessagesForWhatsappChatApi.mockResolvedValue({
                data: mockApiMessages
            });

            // Mock Firebase listener to simulate real-time updates
            const mockFirebaseMessages = [
                {
                    id: 'firebase_msg1',
                    data: () => ({
                        message: 'Real-time message',
                        sender: '1234567890',
                        created_time: '2024-01-01T10:01:00Z',
                        type: 'text'
                    })
                }
            ];

            onSnapshot.mockImplementation((query, callback) => {
                // Simulate real-time update
                callback({
                    docs: mockFirebaseMessages,
                    docChanges: () => [{ type: 'added', doc: mockFirebaseMessages[0] }]
                });
                return jest.fn();
            });

            collection.mockReturnValue({});
            query.mockReturnValue({});
            orderBy.mockReturnValue({});

            const action = fetchWhatsAppMessages({
                thread: mockWhatsAppChat,
                selectedPhone: mockSelectedPhone
            });

            await store.dispatch(action);

            // Verify that the Firebase listener callback was set up
            expect(onSnapshot).toHaveBeenCalled();
        });
    });

    describe('Error Scenarios', () => {
        test('handles API failures during message sending', async () => {
            const mockSelectedChat = {
                id: 'backend_chat_123',
                participants: {
                    data: [{ id: 'participant_456' }]
                }
            };

            const mockSelectedPage = {
                id: 'page_123',
                page_token: 'token_123'
            };

            determineChatType.mockReturnValue('messenger');
            getDualWritePaths.mockReturnValue({
                current: { messages: 'chats/backend_chat_123/messages' },
                legacy: { messages: 'chats/participant_456/messages' },
                chatId: 'backend_chat_123'
            });

            metaService.sendMessageApi.mockRejectedValue(new Error('API failed'));

            const action = sendMessage({
                message: 'Test message',
                selectedChat: mockSelectedChat,
                selectedPage: mockSelectedPage
            });

            const result = await store.dispatch(action);

            expect(result.type).toBe('metaBusinessSuite/sendMessage/rejected');
            expect(result.payload).toBe('API failed');
        });

        test('handles Firebase listener errors gracefully', async () => {
            const mockSelectedChat = {
                id: 'backend_chat_123',
                participants: {
                    data: [{ id: 'participant_456' }]
                }
            };

            determineChatType.mockReturnValue('messenger');
            getDualWritePaths.mockReturnValue({
                current: { messages: 'chats/backend_chat_123/messages' },
                legacy: { messages: 'chats/participant_456/messages' }
            });

            metaService.getMessagesForChatApi.mockResolvedValue([]);

            // Mock Firebase operations to succeed initially
            getDocs.mockResolvedValue({ docs: [] });
            collection.mockReturnValue({});
            query.mockReturnValue({});
            orderBy.mockReturnValue({});

            // Mock onSnapshot to call error callback
            onSnapshot.mockImplementation((query, callback, errorCallback) => {
                errorCallback(new Error('Firebase listener error'));
                return jest.fn();
            });

            // Set up state with latest messages
            store.dispatch({
                type: 'metaBusinessSuite/setLatestMessages',
                payload: [{ id: 'participant_456', sender: 'participant_456' }]
            });

            const action = fetchMessages({ thread: mockSelectedChat });
            const result = await store.dispatch(action);

            // Should still succeed despite listener error
            expect(result.type).toBe('metaBusinessSuite/fetchMessages/fulfilled');
        });

        test('handles malformed chat objects', async () => {
            const malformedChat = {}; // Missing required fields

            determineChatType.mockReturnValue(null);

            const action = fetchMessages({ thread: malformedChat });
            const result = await store.dispatch(action);

            expect(result.type).toBe('metaBusinessSuite/fetchMessages/fulfilled');
            expect(result.payload.messages).toEqual([]);
        });
    });

    describe('State Management Integration', () => {
        test('updates Redux state correctly after successful message send', async () => {
            const mockSelectedChat = {
                id: 'backend_chat_123',
                participants: {
                    data: [{ id: 'participant_456' }]
                }
            };

            const mockSelectedPage = {
                id: 'page_123',
                page_token: 'token_123'
            };

            determineChatType.mockReturnValue('messenger');
            getDualWritePaths.mockReturnValue({
                current: { messages: 'chats/backend_chat_123/messages' },
                legacy: { messages: 'chats/participant_456/messages' },
                chatId: 'backend_chat_123'
            });

            metaService.sendMessageApi.mockResolvedValue({
                message_id: 'api_msg_123'
            });
            setDoc.mockResolvedValue();

            const initialState = store.getState().metaBusinessSuite;
            expect(initialState.loading).toBe(false);

            const action = sendMessage({
                message: 'Test message',
                selectedChat: mockSelectedChat,
                selectedPage: mockSelectedPage
            });

            // Check loading state during dispatch
            const promise = store.dispatch(action);
            const loadingState = store.getState().metaBusinessSuite;
            expect(loadingState.loading).toBe(true);

            await promise;

            // Check final state
            const finalState = store.getState().metaBusinessSuite;
            expect(finalState.loading).toBe(false);
            expect(finalState.error).toBeNull();
        });

        test('handles concurrent message operations correctly', async () => {
            const mockSelectedChat = {
                id: 'backend_chat_123',
                participants: {
                    data: [{ id: 'participant_456' }]
                }
            };

            const mockSelectedPage = {
                id: 'page_123',
                page_token: 'token_123'
            };

            determineChatType.mockReturnValue('messenger');
            getDualWritePaths.mockReturnValue({
                current: { messages: 'chats/backend_chat_123/messages' },
                legacy: { messages: 'chats/participant_456/messages' },
                chatId: 'backend_chat_123'
            });

            metaService.sendMessageApi.mockResolvedValue({
                message_id: 'api_msg_123'
            });
            setDoc.mockResolvedValue();

            // Dispatch multiple messages concurrently
            const promises = [
                store.dispatch(sendMessage({
                    message: 'Message 1',
                    selectedChat: mockSelectedChat,
                    selectedPage: mockSelectedPage
                })),
                store.dispatch(sendMessage({
                    message: 'Message 2',
                    selectedChat: mockSelectedChat,
                    selectedPage: mockSelectedPage
                }))
            ];

            const results = await Promise.all(promises);

            // Both should succeed
            results.forEach(result => {
                expect(result.type).toBe('metaBusinessSuite/sendMessage/fulfilled');
            });

            // Should have made dual writes for both messages
            expect(setDoc).toHaveBeenCalledTimes(4); // 2 messages × 2 paths each
        });
    });
});
