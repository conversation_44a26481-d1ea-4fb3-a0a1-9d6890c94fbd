/**
 * Tests for Firebase Rollback and Cleanup Utilities
 */

import {
    rollbackToChatIdStructure,
    cleanupOldSenderIdPaths,
    getComprehensiveStatus
} from './rollbackCleanup';
import {
    collection,
    doc,
    getDocs,
    setDoc,
    deleteDoc,
    writeBatch
} from 'firebase/firestore';
import { db } from '../firebase.config';
import {
    getDualWritePaths,
    determineChatType
} from '../../services/firebase/collectionPaths';
import { verifyMigrationIntegrity } from './dataMigration';

// Mock Firebase
jest.mock('firebase/firestore');
jest.mock('../firebase.config');
jest.mock('../../services/firebase/collectionPaths');
jest.mock('./dataMigration');
jest.mock('./migrationMonitoring', () => ({
    logMigrationOperation: jest.fn(),
    withPerformanceMonitoring: (name, fn) => fn,
    LOG_LEVELS: { INFO: 'info', SUCCESS: 'success', ERROR: 'error' },
    OPERATION_TYPES: { ROLLBACK: 'rollback', CLEANUP: 'cleanup' }
}));

describe('Rollback and Cleanup Utilities', () => {
    const mockSelectedChat = {
        id: 'chat123',
        sender_id: 'sender456',
        type: 'messenger'
    };

    const mockDualWritePaths = {
        chatId: 'chat123',
        legacy: {
            messages: 'chats/sender456/messages'
        },
        current: {
            messages: 'chats/chat123/messages'
        }
    };

    const mockMessages = [
        {
            id: 'msg1',
            data: () => ({
                message: 'Hello',
                sender: 'sender456',
                created_time: '2024-01-01T10:00:00Z'
            })
        },
        {
            id: 'msg2',
            data: () => ({
                message: 'World',
                sender: 'sender456',
                created_time: '2024-01-01T10:01:00Z'
            })
        }
    ];

    beforeEach(() => {
        jest.clearAllMocks();

        determineChatType.mockReturnValue('messenger');
        getDualWritePaths.mockReturnValue(mockDualWritePaths);

        // Mock Firebase operations
        const mockBatch = {
            set: jest.fn(),
            delete: jest.fn(),
            commit: jest.fn().mockResolvedValue()
        };
        writeBatch.mockReturnValue(mockBatch);

        collection.mockReturnValue({});
        doc.mockReturnValue({});
        getDocs.mockResolvedValue({ docs: [], empty: true });
        setDoc.mockResolvedValue();
        deleteDoc.mockResolvedValue();
    });

    describe('rollbackToChatIdStructure', () => {
        beforeEach(() => {
            // Mock requiresMigration to return true
            jest.doMock('../../services/firebase/collectionPaths', () => ({
                ...jest.requireActual('../../services/firebase/collectionPaths'),
                requiresMigration: jest.fn().mockReturnValue(true)
            }));
        });

        it('should successfully rollback messages from chat ID to sender ID structure', async () => {
            // Mock source collection (chat ID based) with messages
            const sourceSnapshot = {
                docs: mockMessages,
                empty: false
            };

            // Mock destination collection (sender ID based) as empty
            const destSnapshot = {
                docs: [],
                empty: true
            };

            getDocs
                .mockResolvedValueOnce(sourceSnapshot) // Source collection
                .mockResolvedValueOnce(destSnapshot); // Destination collection

            verifyMigrationIntegrity.mockResolvedValue({
                success: true,
                sourceCount: 2,
                destinationCount: 2,
                missingInDestination: [],
                dataIntegrityIssues: []
            });

            const result = await rollbackToChatIdStructure(mockSelectedChat, {
                dryRun: false,
                deleteSource: false
            });

            expect(result.success).toBe(true);
            expect(result.rolledBackCount).toBe(2);
            expect(result.skippedCount).toBe(0);
            expect(result.errorCount).toBe(0);
            expect(result.fromPath).toBe('chats/chat123/messages');
            expect(result.toPath).toBe('chats/sender456/messages');
        });

        it('should perform dry run rollback without making changes', async () => {
            const sourceSnapshot = {
                docs: mockMessages,
                empty: false
            };

            const destSnapshot = {
                docs: [],
                empty: true
            };

            getDocs
                .mockResolvedValueOnce(sourceSnapshot)
                .mockResolvedValueOnce(destSnapshot);

            const result = await rollbackToChatIdStructure(mockSelectedChat, {
                dryRun: true
            });

            expect(result.success).toBe(true);
            expect(result.rolledBackCount).toBe(2);
            expect(result.skippedCount).toBe(0);
            expect(writeBatch).not.toHaveBeenCalled();
        });

        it('should skip messages that already exist in destination', async () => {
            const sourceSnapshot = {
                docs: mockMessages,
                empty: false
            };

            // Mock destination with one existing message
            const destSnapshot = {
                docs: [{ id: 'msg1' }],
                empty: false
            };

            getDocs
                .mockResolvedValueOnce(sourceSnapshot)
                .mockResolvedValueOnce(destSnapshot);

            verifyMigrationIntegrity.mockResolvedValue({
                success: true,
                sourceCount: 2,
                destinationCount: 1,
                missingInDestination: [],
                dataIntegrityIssues: []
            });

            const result = await rollbackToChatIdStructure(mockSelectedChat, {
                dryRun: false
            });

            expect(result.success).toBe(true);
            expect(result.rolledBackCount).toBe(1); // Only msg2 should be rolled back
            expect(result.skippedCount).toBe(1); // msg1 already exists
        });

        it('should handle rollback with source deletion', async () => {
            const sourceSnapshot = {
                docs: mockMessages,
                empty: false
            };

            const destSnapshot = {
                docs: [],
                empty: true
            };

            getDocs
                .mockResolvedValueOnce(sourceSnapshot)
                .mockResolvedValueOnce(destSnapshot);

            verifyMigrationIntegrity.mockResolvedValue({
                success: true,
                sourceCount: 2,
                destinationCount: 2,
                missingInDestination: [],
                dataIntegrityIssues: []
            });

            const mockBatch = writeBatch();

            const result = await rollbackToChatIdStructure(mockSelectedChat, {
                dryRun: false,
                deleteSource: true
            });

            expect(result.success).toBe(true);
            expect(result.rolledBackCount).toBe(2);
            expect(mockBatch.set).toHaveBeenCalledTimes(2);
            expect(mockBatch.commit).toHaveBeenCalledTimes(2); // Once for rollback, once for deletion
        });

        it('should fail rollback if data integrity verification fails', async () => {
            verifyMigrationIntegrity.mockResolvedValue({
                success: false,
                missingInDestination: ['msg1'],
                dataIntegrityIssues: []
            });

            const result = await rollbackToChatIdStructure(mockSelectedChat, {
                dryRun: false,
                verifyIntegrity: true
            });

            expect(result.success).toBe(false);
            expect(result.errors).toHaveLength(1);
            expect(result.errors[0].type).toBe('rollback_error');
            expect(result.errors[0].message).toContain('Data integrity verification failed');
        });

        it('should handle empty source collection gracefully', async () => {
            const emptySnapshot = {
                docs: [],
                empty: true
            };

            getDocs.mockResolvedValue(emptySnapshot);

            const result = await rollbackToChatIdStructure(mockSelectedChat);

            expect(result.success).toBe(true);
            expect(result.rolledBackCount).toBe(0);
            expect(result.skippedCount).toBe(0);
        });

        it('should handle chat types that do not require rollback', async () => {
            determineChatType.mockReturnValue('whatsapp');

            // Mock requiresMigration to return false for whatsapp
            jest.doMock('../../services/firebase/collectionPaths', () => ({
                ...jest.requireActual('../../services/firebase/collectionPaths'),
                requiresMigration: jest.fn().mockReturnValue(false)
            }));

            const result = await rollbackToChatIdStructure(mockSelectedChat);

            expect(result.success).toBe(true);
            expect(result.rolledBackCount).toBe(0);
            expect(result.skippedCount).toBe(0);
        });

        it('should handle batch processing errors gracefully', async () => {
            const sourceSnapshot = {
                docs: mockMessages,
                empty: false
            };

            const destSnapshot = {
                docs: [],
                empty: true
            };

            getDocs
                .mockResolvedValueOnce(sourceSnapshot)
                .mockResolvedValueOnce(destSnapshot);

            verifyMigrationIntegrity.mockResolvedValue({
                success: true,
                sourceCount: 2,
                destinationCount: 2,
                missingInDestination: [],
                dataIntegrityIssues: []
            });

            // Mock batch commit to fail
            const mockBatch = {
                set: jest.fn(),
                delete: jest.fn(),
                commit: jest.fn().mockRejectedValue(new Error('Batch commit failed'))
            };
            writeBatch.mockReturnValue(mockBatch);

            const result = await rollbackToChatIdStructure(mockSelectedChat);

            expect(result.success).toBe(false);
            expect(result.errorCount).toBe(2);
            expect(result.rolledBackCount).toBe(0);
        });
    });

    describe('cleanupOldSenderIdPaths', () => {
        beforeEach(() => {
            jest.doMock('../../services/firebase/collectionPaths', () => ({
                ...jest.requireActual('../../services/firebase/collectionPaths'),
                requiresMigration: jest.fn().mockReturnValue(true)
            }));
        });

        it('should successfully cleanup old sender ID paths', async () => {
            const snapshot = {
                docs: mockMessages,
                empty: false
            };

            getDocs.mockResolvedValue(snapshot);

            verifyMigrationIntegrity.mockResolvedValue({
                success: true,
                sourceCount: 2,
                destinationCount: 2,
                missingInDestination: [],
                dataIntegrityIssues: []
            });

            const result = await cleanupOldSenderIdPaths(mockSelectedChat, {
                dryRun: false
            });

            expect(result.success).toBe(true);
            expect(result.deletedCount).toBe(2);
            expect(result.errorCount).toBe(0);
            expect(result.cleanedPath).toBe('chats/sender456/messages');
        });

        it('should perform dry run cleanup without making changes', async () => {
            const snapshot = {
                docs: mockMessages,
                empty: false
            };

            getDocs.mockResolvedValue(snapshot);

            const result = await cleanupOldSenderIdPaths(mockSelectedChat, {
                dryRun: true
            });

            expect(result.success).toBe(true);
            expect(result.deletedCount).toBe(2);
            expect(writeBatch).not.toHaveBeenCalled();
        });

        it('should fail cleanup if migration verification fails', async () => {
            verifyMigrationIntegrity.mockResolvedValue({
                success: false,
                missingInDestination: ['msg1'],
                dataIntegrityIssues: []
            });

            const result = await cleanupOldSenderIdPaths(mockSelectedChat, {
                dryRun: false,
                verifyMigration: true
            });

            expect(result.success).toBe(false);
            expect(result.errors).toHaveLength(1);
            expect(result.errors[0].type).toBe('cleanup_error');
            expect(result.errors[0].message).toContain('Migration verification failed');
        });

        it('should handle empty collection gracefully', async () => {
            const emptySnapshot = {
                docs: [],
                empty: true
            };

            getDocs.mockResolvedValue(emptySnapshot);

            const result = await cleanupOldSenderIdPaths(mockSelectedChat);

            expect(result.success).toBe(true);
            expect(result.deletedCount).toBe(0);
        });

        it('should handle batch deletion errors gracefully', async () => {
            const snapshot = {
                docs: mockMessages,
                empty: false
            };

            getDocs.mockResolvedValue(snapshot);

            verifyMigrationIntegrity.mockResolvedValue({
                success: true,
                sourceCount: 2,
                destinationCount: 2,
                missingInDestination: [],
                dataIntegrityIssues: []
            });

            // Mock batch commit to fail
            const mockBatch = {
                delete: jest.fn(),
                commit: jest.fn().mockRejectedValue(new Error('Batch delete failed'))
            };
            writeBatch.mockReturnValue(mockBatch);

            const result = await cleanupOldSenderIdPaths(mockSelectedChat);

            expect(result.success).toBe(false);
            expect(result.errorCount).toBe(2);
            expect(result.deletedCount).toBe(0);
        });
    });

    describe('getComprehensiveStatus', () => {
        beforeEach(() => {
            jest.doMock('../../services/firebase/collectionPaths', () => ({
                ...jest.requireActual('../../services/firebase/collectionPaths'),
                requiresMigration: jest.fn().mockReturnValue(true)
            }));
        });

        it('should return correct status for not migrated data', async () => {
            // Legacy has data, current is empty
            getDocs
                .mockResolvedValueOnce({ docs: mockMessages, empty: false }) // Legacy
                .mockResolvedValueOnce({ docs: [], empty: true }); // Current

            const result = await getComprehensiveStatus(mockSelectedChat);

            expect(result.requiresOperations).toBe(true);
            expect(result.status).toBe('not_migrated');
            expect(result.recommendedAction).toBe('migrate');
            expect(result.legacyCount).toBe(2);
            expect(result.currentCount).toBe(0);
            expect(result.canRollback).toBe(false);
            expect(result.canCleanup).toBe(false);
        });

        it('should return correct status for fully migrated and cleaned data', async () => {
            // Legacy is empty, current has data
            getDocs
                .mockResolvedValueOnce({ docs: [], empty: true }) // Legacy
                .mockResolvedValueOnce({ docs: mockMessages, empty: false }); // Current

            const result = await getComprehensiveStatus(mockSelectedChat);

            expect(result.status).toBe('fully_migrated_and_cleaned');
            expect(result.recommendedAction).toBe('none');
            expect(result.legacyCount).toBe(0);
            expect(result.currentCount).toBe(2);
            expect(result.canRollback).toBe(true);
            expect(result.canCleanup).toBe(false);
        });

        it('should return correct status for migrated data needing cleanup', async () => {
            // Both legacy and current have data
            getDocs
                .mockResolvedValueOnce({ docs: mockMessages, empty: false }) // Legacy
                .mockResolvedValueOnce({ docs: mockMessages, empty: false }); // Current

            const result = await getComprehensiveStatus(mockSelectedChat);

            expect(result.status).toBe('migrated_needs_cleanup');
            expect(result.recommendedAction).toBe('cleanup');
            expect(result.legacyCount).toBe(2);
            expect(result.currentCount).toBe(2);
            expect(result.canRollback).toBe(true);
            expect(result.canCleanup).toBe(true);
        });

        it('should return correct status for no data', async () => {
            // Both collections are empty
            getDocs
                .mockResolvedValueOnce({ docs: [], empty: true }) // Legacy
                .mockResolvedValueOnce({ docs: [], empty: true }); // Current

            const result = await getComprehensiveStatus(mockSelectedChat);

            expect(result.status).toBe('no_data');
            expect(result.recommendedAction).toBe('none');
            expect(result.legacyCount).toBe(0);
            expect(result.currentCount).toBe(0);
            expect(result.canRollback).toBe(false);
            expect(result.canCleanup).toBe(false);
        });

        it('should handle chat types that do not require operations', async () => {
            jest.doMock('../../services/firebase/collectionPaths', () => ({
                ...jest.requireActual('../../services/firebase/collectionPaths'),
                requiresMigration: jest.fn().mockReturnValue(false)
            }));

            const result = await getComprehensiveStatus(mockSelectedChat);

            expect(result.requiresOperations).toBe(false);
            expect(result.status).toBe('not_required');
        });

        it('should handle errors gracefully', async () => {
            getDocs.mockRejectedValue(new Error('Firebase error'));

            const result = await getComprehensiveStatus(mockSelectedChat);

            expect(result.requiresOperations).toBe(false);
            expect(result.error).toBe('Firebase error');
        });
    });

    describe('Error Handling', () => {
        it('should handle missing selectedChat parameter', async () => {
            const result = await rollbackToChatIdStructure(null);

            expect(result.success).toBe(false);
            expect(result.errors).toHaveLength(1);
            expect(result.errors[0].message).toBe('selectedChat is required');
        });

        it('should handle unknown chat type', async () => {
            determineChatType.mockReturnValue(null);

            const result = await rollbackToChatIdStructure(mockSelectedChat);

            expect(result.success).toBe(false);
            expect(result.errors).toHaveLength(1);
            expect(result.errors[0].message).toBe('Unable to determine chat type');
        });

        it('should handle missing dual write paths', async () => {
            getDualWritePaths.mockReturnValue({
                legacy: { messages: null },
                current: { messages: null }
            });

            const result = await rollbackToChatIdStructure(mockSelectedChat);

            expect(result.success).toBe(false);
            expect(result.errors).toHaveLength(1);
            expect(result.errors[0].message).toBe('Unable to determine source and destination paths');
        });
    });
});
