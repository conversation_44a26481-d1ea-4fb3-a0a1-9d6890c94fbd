import React from "react";
import { Navigate } from "react-router-dom";
import { useSelector } from "react-redux";
import { shouldShowPackageFeatures } from "../../config/packageVisibility";

/**
 * PackageRouteGuard Component
 *
 * This component protects package-related routes by checking if the current user
 * should have access to package features. Users in the exclusion list will be
 * redirected to the unauthorized page.
 *
 * @param {Object} props - Component props
 * @param {React.ReactNode} props.children - Child components to render if access is allowed
 * @returns {React.ReactNode} - Either the children or a redirect to unauthorized page
 */
function PackageRouteGuard({ children }) {
  const { user } = useSelector((state) => state.auth);

  // Get the user ID from the auth state
  const userId = user?.user?.id;

  // Check if the user should see package features
  const canAccessPackageFeatures = shouldShowPackageFeatures(userId);

  // If user is excluded from package features, redirect to unauthorized page
  if (!canAccessPackageFeatures) {
    return <Navigate to="/unauthorized" replace />;
  }

  // If user can access package features, render the children
  return children;
}

export default PackageRouteGuard;
