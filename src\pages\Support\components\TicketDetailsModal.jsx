import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
  Badge,
  Form,
  Accordion,
} from "react-bootstrap";
import {
  FaPaperclip,
  FaImage,
  FaFilePdf,
  FaFileAlt,
  FaFileVideo,
  FaFileAudio,
  FaUser,
  FaHeadset,
  FaEye,
  FaTimes,
  FaReply,
  FaHistory,
} from "react-icons/fa";
import CenteredModal from "../../../components/Shared/modals/CenteredModal/CenteredModal";
import { useDropzone } from "react-dropzone";
import "./TicketDetailsModal.css";
import { useState, useEffect, useCallback } from "react";
import { replyToTicket } from "../../../services/tickets";
import {
  showErrorToast,
  showSuccessToast,
} from "../../../utils/toast-success-error";

const TicketDetailsModal = ({
  ticket: initialTicket,
  onClose,
  onReply,
  onStatusChange,
  isAdmin = false,
  onTicketUpdate,
}) => {
  const [ticket, setTicket] = useState(initialTicket);
  const [replyText, setReplyText] = useState("");
  const [replyAttachments, setReplyAttachments] = useState([]);
  const [showDropzone, setShowDropzone] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingTicket, setIsLoadingTicket] = useState(false);
  const [previewModal, setPreviewModal] = useState({
    show: false,
    url: "",
    type: "",
    filename: "",
  });
  const [showValidation, setShowValidation] = useState(false);

  // Update ticket when initialTicket changes
  useEffect(() => {
    if (initialTicket) {
      setIsLoadingTicket(true);
      setTicket(initialTicket);
      setReplyAttachments([]);
      setShowDropzone(false);
      setIsLoading(false);
      setIsLoadingTicket(false);
    }
  }, [initialTicket]);

  // Handle file drop
  const onDrop = useCallback((acceptedFiles) => {
    // Add a preview URL to each file for image display
    const filesWithPreview = acceptedFiles.map((file) => {
      return Object.assign(file, {
        preview: URL.createObjectURL(file),
      });
    });

    setReplyAttachments((prev) => [...prev, ...filesWithPreview]);
  }, []);

  // Revoke object URLs on component unmount to avoid memory leaks
  useEffect(() => {
    return () => {
      replyAttachments.forEach((file) => {
        if (file.preview) URL.revokeObjectURL(file.preview);
      });
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Setup dropzone
  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      "image/*": [],
      "application/pdf": [],
      "text/plain": [],
      "application/msword": [],
      "application/vnd.openxmlformats-officedocument.wordprocessingml.document":
        [],
    },
  });

  // Remove attachment (by index) and revoke its object URL
  const removeAttachment = (index) => {
    setReplyAttachments((prev) => {
      const fileToRemove = prev[index];
      if (fileToRemove && fileToRemove.preview) {
        URL.revokeObjectURL(fileToRemove.preview);
      }
      return prev.filter((_, i) => i !== index);
    });
  };

  // Handle sending reply – reply text is required
  const handleSendReply = async () => {
    if (!replyText.trim()) {
      setShowValidation(true);
      showErrorToast("Reply description is required");
      return;
    }

    setIsLoading(true);
    try {
      // Create form data for the reply
      const formData = new FormData();

      // Always add content field, even if empty
      formData.append("content", replyText.trim());

      // Add each attachment to the formData
      if (replyAttachments.length > 0) {
        replyAttachments.forEach((file, index) => {
          formData.append(`attachments[${index}]`, file);
        });
      }

      // Use the provided onReply function or fallback to the default
      const response = onReply
        ? await onReply(ticket.id, formData)
        : await replyToTicket(ticket.id, formData);

      if (response) {
        // Merge the new data with the existing ticket to ensure we keep fields
        // (like user) that might be missing from the API response.
        const updatedTicket = {
          ...ticket,
          ...(response.data || response),
          // Prefer fresh histories from the response but fallback to existing
          histories: (response.data || response).histories || ticket.histories,
          // Preserve the user field if it is not returned in the response
          user: (response.data || response).user || ticket.user,
        };

        setTicket(updatedTicket);

        // Clear the reply form
        setReplyText("");
        setReplyAttachments([]);
        setShowDropzone(false);
        showSuccessToast("Reply sent successfully");

        // Preserve histories and attachments if they're missing in the response
        if (!updatedTicket.histories && ticket.histories) {
          updatedTicket.histories = ticket.histories;
        }

        // Preserve user info if missing from the response
        if (!updatedTicket.user && ticket.user) {
          updatedTicket.user = ticket.user;
        }

        // Update the ticket state with the merged data
        setTicket(updatedTicket);

        // Notify parent component if a callback is provided
        if (onTicketUpdate) {
          onTicketUpdate(updatedTicket);
        }
      } else {
        showErrorToast("Failed to send reply");
      }
    } catch (error) {
      console.error("Error sending reply:", error);
      showErrorToast("Failed to send reply");
    } finally {
      setIsLoading(false);
    }
  };

  // Handle status change (admin only)
  const handleStatusChange = async (status) => {
    setIsLoading(true);
    try {
      const response = await onStatusChange(ticket.id, status);

      let updatedTicket;

      if (response && response.data) {
        updatedTicket = response.data;
      } else if (response && typeof response === "object" && response.id) {
        updatedTicket = response;
      } else {
        updatedTicket = { ...ticket, status };
      }
      if (!updatedTicket.histories && ticket.histories) {
        updatedTicket.histories = ticket.histories;
      }
      if (!updatedTicket.user && ticket.user) {
        updatedTicket.user = ticket.user;
      }

      setTicket(updatedTicket);

      // Notify parent component if a callback is provided
      if (onTicketUpdate) {
        onTicketUpdate(updatedTicket);
      }
    } catch (error) {
      console.error("Error updating status:", error);
    } finally {
      setIsLoading(false);
    }
  };

  if (!ticket) return null;

  const getStatusBadgeVariant = (status) => {
    switch (status.toLowerCase()) {
      case "open":
        return "primary";
      case "in_progress":
        return "warning";
      case "closed":
      case "resolved":
        return "success";
      case "pending":
        return "info";
      case "cancelled":
        return "danger";
      default:
        return "secondary";
    }
  };

  const getPriorityBadgeVariant = (priority) => {
    switch (priority?.toLowerCase()) {
      case "high":
        return "danger";
      case "medium":
        return "warning";
      case "low":
        return "success";
      default:
        return "secondary";
    }
  };

  const getAttachmentIcon = (attachment) => {
    // Handle both string filenames and object attachments
    const filename =
      typeof attachment === "string"
        ? attachment
        : attachment.filename || attachment.path || attachment.name || "";

    if (!filename) return <FaPaperclip />;

    const extension = filename.split(".").pop().toLowerCase();

    if (["png", "jpg", "jpeg", "gif"].includes(extension)) {
      return <FaImage className="text-primary" />;
    } else if (["pdf"].includes(extension)) {
      return <FaFilePdf className="text-danger" />;
    } else if (["doc", "docx"].includes(extension)) {
      return <FaFileAlt className="text-primary" />;
    } else if (["mp4", "mov", "avi"].includes(extension)) {
      return <FaFileVideo className="text-success" />;
    } else if (["mp3", "wav"].includes(extension)) {
      return <FaFileAudio className="text-warning" />;
    } else if (["txt", "log"].includes(extension)) {
      return <FaFileAlt className="text-secondary" />;
    } else {
      return <FaPaperclip />;
    }
  };

  // Function to get preview URL for attachments
  const getPreviewUrl = (attachment) => {
    // For direct file_url (preferred method)
    if (attachment && attachment.file_url) {
      return attachment.file_url;
    }

    // For string attachments - don't use process.env
    if (typeof attachment === "string") {
      return attachment;
    }

    // For object attachments with file_path - don't use process.env
    if (attachment && attachment.file_path) {
      return attachment.file_path;
    }

    // For local file previews (e.g., from dropzone)
    if (attachment && attachment.preview) {
      return attachment.preview;
    }

    // For other URL formats
    if (attachment && attachment.url) {
      return attachment.url;
    }

    // For path-based attachments - don't use process.env
    if (attachment && attachment.path) {
      return attachment.path;
    }

    return null;
  };

  // Function to check if attachment is an image
  const isImageAttachment = (attachment) => {
    // Handle string paths
    if (typeof attachment === "string") {
      const extension = attachment.split(".").pop().toLowerCase();
      return ["png", "jpg", "jpeg", "gif"].includes(extension);
    }

    // Handle object attachments
    const filename =
      attachment.filename ||
      attachment.file_path?.split("/").pop() ||
      attachment.path?.split("/").pop() ||
      attachment.name ||
      "";

    if (!filename) return false;

    const extension = filename.split(".").pop().toLowerCase();
    return ["png", "jpg", "jpeg", "gif"].includes(extension);
  };

  // Function to handle preview in modal
  const handlePreviewInModal = (attachment) => {
    // Get filename
    let filename;
    if (typeof attachment === "string") {
      filename = attachment;
    } else if (attachment.file_path) {
      filename = attachment.file_path.split("/").pop();
    } else if (attachment.filename) {
      filename = attachment.filename;
    } else if (attachment.path) {
      filename = attachment.path.split("/").pop();
    } else if (attachment.name) {
      filename = attachment.name;
    } else {
      filename = "attachment";
    }

    // Get URL - prefer file_url if available
    const previewUrl = attachment.file_url;
    if (!previewUrl) return;

    const extension = filename.split(".").pop().toLowerCase();

    let type = "other";
    if (["png", "jpg", "jpeg", "gif"].includes(extension)) {
      type = "image";
    } else if (extension === "pdf") {
      type = "pdf";
    } else if (["mp4", "mov", "avi"].includes(extension)) {
      type = "video";
    } else if (["mp3", "wav"].includes(extension)) {
      type = "audio";
    }

    setPreviewModal({
      show: true,
      url: previewUrl,
      type,
      filename,
    });
  };

  // Function to check if file is previewable
  const isFilePreviewable = (filename) => {
    if (!filename) return false;
    const extension = filename.split(".").pop().toLowerCase();
    // Only include file types that can be previewed
    return [
      "png",
      "jpg",
      "jpeg",
      "gif",
      "pdf",
      "mp4",
      "mov",
      "avi",
      "mp3",
      "wav",
    ].includes(extension);
  };

  // Function to close preview modal
  const closePreviewModal = () => {
    setPreviewModal({
      show: false,
      url: "",
      type: "",
      filename: "",
    });
  };

  // Function to get icon indicating who replied (support or client)
  const getResponderIcon = (historyItem) => {
    return historyItem.admin_name ? (
      <FaHeadset className="text-primary" />
    ) : (
      <FaUser className="text-secondary" />
    );
  };

  // Function to get display name for history item
  const getHistoryUserName = (historyItem) => {
    if (historyItem.admin_name) return historyItem.admin_name;
    if (historyItem.user && typeof historyItem.user === "object")
      return historyItem.user.name;
    if (historyItem.user) return historyItem.user;
    return ticket?.user?.name || "User";
  };

  // Helper to get separate date and time parts
  const getDateAndTime = (dateString) => {
    if (!dateString) return { date: "", time: "" };
    const date = new Date(dateString);
    if (isNaN(date)) return { date: dateString, time: "" };

    const pad = (n) => n.toString().padStart(2, "0");

    const year = date.getFullYear();
    const month = pad(date.getMonth() + 1);
    const day = pad(date.getDate());
    const hours = pad(date.getHours());
    const minutes = pad(date.getMinutes());

    return { date: `${year}-${month}-${day}`, time: `${hours}:${minutes}` };
  };

  const formatDate = (dateString) => {
    const { date, time } = getDateAndTime(dateString);
    return `${date} ${time}`.trim();
  };

  // Function to format timestamp
  const formatTimestamp = (historyItem) => {
    const timestamp = historyItem.created_at || historyItem.timestamp;
    if (!timestamp) return "";
    return getDateAndTime(timestamp);
  };

  const isSendDisabled = isLoading;

  return (
    <>
      <CenteredModal show={true} onHide={onClose} size="lg">
        <Modal.Header closeButton>
          <Modal.Title>
            Ticket #{ticket.id}: {ticket.title}
          </Modal.Title>
        </Modal.Header>
        <Modal.Body>
          {isLoadingTicket ? (
            <div className="text-center p-4">
              <div className="spinner-border text-primary" role="status">
                <span className="visually-hidden">Loading...</span>
              </div>
              <p className="mt-2">Loading ticket details...</p>
            </div>
          ) : (
            <div className="ticket-details">
              <Row className="mb-3">
                <Col xs={4} md={3} className="fw-bold">
                  Status:
                </Col>
                <Col xs={8} md={9}>
                  {ticket?.status && isAdmin ? (
                    <Form.Select
                      className="w-50"
                      value={ticket.status}
                      onChange={(e) => handleStatusChange(e.target.value)}
                    >
                      <option value="open">Open</option>
                      <option value="in_progress">In Progress</option>
                      <option value="closed">Closed</option>
                    </Form.Select>
                  ) : (
                    <Badge
                      bg={getStatusBadgeVariant(ticket.status)}
                      className="p-2"
                    >
                      {ticket.status}
                    </Badge>
                  )}
                </Col>
              </Row>

              {ticket.priority && (
                <Row className="mb-3">
                  <Col xs={4} md={3} className="fw-bold">
                    Priority:
                  </Col>
                  <Col xs={8} md={9}>
                    <Badge
                      bg={getPriorityBadgeVariant(ticket.priority)}
                      className="p-2"
                    >
                      {ticket.priority}
                    </Badge>
                  </Col>
                </Row>
              )}

              <Row className="mb-3">
                <Col xs={4} md={3} className="fw-bold">
                  Created:
                </Col>
                <Col xs={8} md={9}>
                  {ticket.created_at
                    ? (() => {
                        const { date, time } = getDateAndTime(
                          ticket.created_at
                        );
                        return (
                          <>
                            <span>{date}</span>
                            <br />
                            <span>{time}</span>
                          </>
                        );
                      })()
                    : ticket.lastUpdate}
                </Col>
              </Row>

              <Row className="mb-3">
                <Col xs={4} md={3} className="fw-bold">
                  <p>Client: (#{ticket?.user?.id})</p>
                </Col>
                <Col xs={8} md={9}>
                  {ticket.user && <p>{ticket?.user?.name}</p>}
                </Col>
              </Row>

              <Row className="mb-4">
                <Col xs={12}>
                  <div className="fw-bold mb-2">Description:</div>
                  <div className="ticket-description p-3 border rounded bg-light">
                    {ticket.description}
                  </div>
                </Col>
              </Row>

              {/* Ticket History Section with Accordion */}
              {ticket.histories && ticket.histories.length > 0 && (
                <Row className="mb-4">
                  <Col xs={12}>
                    <div className="d-flex align-items-center fw-bold mb-2">
                      <FaHistory className="me-2" /> Ticket History:
                    </div>
                    <Accordion className="ticket-history-accordion">
                      {ticket.histories.map((historyItem) => (
                        <Accordion.Item
                          eventKey={historyItem.id.toString()}
                          key={historyItem.id}
                        >
                          <Accordion.Header>
                            <div className="d-flex align-items-center w-100">
                              <span className="me-2">
                                {getResponderIcon(historyItem)}
                              </span>
                              <span className="fw-bold me-2">
                                {getHistoryUserName(historyItem)}
                              </span>
                              <span className="flex-grow-1 text-truncate">
                                {historyItem.content}
                              </span>
                              {(() => {
                                const { date, time } =
                                  formatTimestamp(historyItem);
                                return (
                                  <small className="text-muted ms-2 text-end">
                                    {date}
                                    <br />
                                    {time}
                                  </small>
                                );
                              })()}
                            </div>
                          </Accordion.Header>
                          <Accordion.Body>
                            <p>{historyItem.content}</p>

                            {historyItem.attachments &&
                              historyItem.attachments.length > 0 && (
                                <Row>
                                  {historyItem.attachments.map(
                                    (attachment, index) => {
                                      const filename = attachment.file_path
                                        ? attachment.file_path.split("/").pop()
                                        : `attachment-${index}`;
                                      const previewUrl = attachment.file_url;
                                      const extension = filename
                                        .split(".")
                                        .pop()
                                        .toLowerCase();
                                      const isImage = [
                                        "png",
                                        "jpg",
                                        "jpeg",
                                        "gif",
                                      ].includes(extension);
                                      const isPdf = extension === "pdf";
                                      const isVideo = [
                                        "mp4",
                                        "mov",
                                        "avi",
                                      ].includes(extension);
                                      const isAudio = ["mp3", "wav"].includes(
                                        extension
                                      );
                                      const isDocument = [
                                        "doc",
                                        "docx",
                                        "txt",
                                      ].includes(extension);
                                      const isPreviewable =
                                        isImage || isPdf || isVideo || isAudio;
                                      const canPreview =
                                        isPreviewable && !isDocument;

                                      return (
                                        <Col
                                          lg={4}
                                          md={6}
                                          sm={12}
                                          key={index}
                                          className="mb-3"
                                        >
                                          <div className="attachment-card">
                                            <div
                                              className="attachment-preview"
                                              onClick={
                                                canPreview
                                                  ? () =>
                                                      handlePreviewInModal(
                                                        attachment
                                                      )
                                                  : undefined
                                              }
                                              style={{
                                                cursor: canPreview
                                                  ? "pointer"
                                                  : "default",
                                              }}
                                            >
                                              {isImage && previewUrl ? (
                                                <img
                                                  src={previewUrl}
                                                  alt={filename}
                                                  className="img-preview"
                                                />
                                              ) : isPdf ? (
                                                <div className="pdf-preview-container">
                                                  <FaFilePdf
                                                    className="text-danger"
                                                    style={{
                                                      fontSize: "3rem",
                                                    }}
                                                  />
                                                  <span className="preview-label mt-2">
                                                    PDF Document
                                                  </span>
                                                </div>
                                              ) : isDocument ? (
                                                <div className="document-preview-container">
                                                  <FaFileAlt
                                                    className="text-primary"
                                                    style={{
                                                      fontSize: "3rem",
                                                    }}
                                                  />
                                                  <span className="preview-label mt-2">
                                                    Document
                                                  </span>
                                                </div>
                                              ) : isVideo ? (
                                                <div className="video-preview-container">
                                                  <FaFileVideo
                                                    className="text-success"
                                                    style={{
                                                      fontSize: "3rem",
                                                    }}
                                                  />
                                                  <span className="preview-label mt-2">
                                                    Video
                                                  </span>
                                                </div>
                                              ) : isAudio ? (
                                                <div className="audio-preview-container">
                                                  <FaFileAudio
                                                    className="text-warning"
                                                    style={{
                                                      fontSize: "3rem",
                                                    }}
                                                  />
                                                  <span className="preview-label mt-2">
                                                    Audio
                                                  </span>
                                                </div>
                                              ) : (
                                                <div className="file-icon-container">
                                                  {getAttachmentIcon(
                                                    attachment
                                                  )}
                                                  <span className="file-type">
                                                    {extension}
                                                  </span>
                                                </div>
                                              )}
                                            </div>
                                            <div className="attachment-info">
                                              <div
                                                className="attachment-filename"
                                                title={filename}
                                              >
                                                {filename.length > 20
                                                  ? filename.substring(0, 17) +
                                                    "..."
                                                  : filename}
                                              </div>
                                              <div className="attachment-actions">
                                                {canPreview && (
                                                  <Button
                                                    variant="outline-secondary"
                                                    size="sm"
                                                    className="preview-btn"
                                                    onClick={() =>
                                                      handlePreviewInModal(
                                                        attachment
                                                      )
                                                    }
                                                    disabled={!previewUrl}
                                                  >
                                                    <FaEye className="me-1" />
                                                    Preview
                                                  </Button>
                                                )}
                                              </div>
                                            </div>
                                          </div>
                                        </Col>
                                      );
                                    }
                                  )}
                                </Row>
                              )}
                          </Accordion.Body>
                        </Accordion.Item>
                      ))}
                    </Accordion>
                  </Col>
                </Row>
              )}

              <Row>
                <Col xs={12}>
                  <div className="fw-bold mb-2">
                    Reply <span className="text-danger">*</span>
                  </div>
                  <div className="reply-box">
                    <textarea
                      className={`form-control mb-2 ${
                        showValidation && replyText.trim().length === 0
                          ? "is-invalid"
                          : ""
                      }`}
                      rows="3"
                      placeholder="Type your reply here..."
                      value={replyText}
                      onChange={(e) => setReplyText(e.target.value)}
                      required
                    ></textarea>
                    {showValidation && replyText.trim().length === 0 && (
                      <div className="invalid-feedback d-block">
                        Reply description is required.
                      </div>
                    )}

                    {/* Dropzone for attachments */}
                    {showDropzone && (
                      <div className="attachment-dropzone mb-3">
                        <div
                          {...getRootProps({
                            className: `dropzone compact-dropzone ${
                              isDragActive ? "active" : ""
                            }`,
                          })}
                        >
                          <input {...getInputProps()} />
                          {isDragActive ? (
                            <p>Drop the files here ...</p>
                          ) : (
                            <p>
                              <FaPaperclip className="me-1" />
                              Drag 'n' drop files here, or click to select files
                            </p>
                          )}
                        </div>
                      </div>
                    )}

                    {/* Attachment previews */}
                    {replyAttachments.length > 0 && (
                      <div className="reply-attachments-preview mb-3">
                        <h6>
                          <FaPaperclip className="me-1" />
                          Attachments ({replyAttachments.length}):
                        </h6>
                        <div className="attachments-grid">
                          {replyAttachments.map((file, index) => (
                            <div
                              key={file.name + index}
                              className="attachment-card"
                            >
                              <div className="attachment-preview">
                                {file.type.startsWith("image/") ? (
                                  <img
                                    src={file.preview}
                                    alt={file.name}
                                    className="img-preview"
                                  />
                                ) : (
                                  <div className="file-icon-container">
                                    {getAttachmentIcon(file)}
                                    <span className="file-type">
                                      {file.name.split(".").pop().toLowerCase()}
                                    </span>
                                  </div>
                                )}
                              </div>
                              <div className="attachment-info">
                                <div
                                  className="attachment-filename"
                                  title={file.name}
                                >
                                  {file.name.length > 20
                                    ? file.name.substring(0, 17) + "..."
                                    : file.name}
                                </div>
                                <Button
                                  variant="outline-danger"
                                  size="sm"
                                  className="remove-attachment-btn"
                                  onClick={() => removeAttachment(index)}
                                >
                                  <FaTimes className="me-1" /> Remove
                                </Button>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                </Col>
              </Row>
            </div>
          )}
        </Modal.Body>
        <Modal.Footer>
          <div className="d-flex justify-content-between w-100">
            <div>
              <Button
                variant="outline-secondary"
                onClick={() => setShowDropzone(!showDropzone)}
                className="me-2"
              >
                <FaPaperclip className="me-1" />
                {showDropzone ? "Cancel Attachment" : "Attach Files"}
              </Button>
            </div>
            <div>
              <Button
                variant="primary"
                onClick={handleSendReply}
                disabled={isSendDisabled}
                className="send-reply-btn"
                style={isSendDisabled ? { pointerEvents: "auto" } : {}}
              >
                {isLoading ? (
                  <>
                    <span
                      className="spinner-border spinner-border-sm me-2"
                      role="status"
                      aria-hidden="true"
                    ></span>
                    Sending...
                  </>
                ) : (
                  <>
                    <FaReply className="me-1" /> Send Reply
                  </>
                )}
              </Button>
            </div>
          </div>
        </Modal.Footer>
      </CenteredModal>

      {/* Preview Modal */}
      <CenteredModal
        show={previewModal.show}
        onHide={closePreviewModal}
        size="lg"
        className="file-preview-modal"
      >
        <Modal.Header closeButton>
          <Modal.Title>{previewModal.filename}</Modal.Title>
        </Modal.Header>
        <Modal.Body className="p-0">
          {previewModal.type === "image" && (
            <div className="image-preview-container">
              <img
                src={previewModal.url}
                alt={previewModal.filename}
                className="img-fluid w-100"
              />
            </div>
          )}
          {previewModal.type === "pdf" && (
            <div className="pdf-embed-container">
              <embed
                src={previewModal.url}
                type="application/pdf"
                width="100%"
                height="600px"
              />
            </div>
          )}
          {previewModal.type === "video" && (
            <div className="video-preview-container">
              <video
                src={previewModal.url}
                controls
                className="w-100"
                height="auto"
              ></video>
            </div>
          )}
          {previewModal.type === "audio" && (
            <div className="audio-preview-container p-4 text-center">
              <audio src={previewModal.url} controls className="w-100"></audio>
            </div>
          )}
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={closePreviewModal}>
            Close
          </Button>
        </Modal.Footer>
      </CenteredModal>
    </>
  );
};

export default TicketDetailsModal;
