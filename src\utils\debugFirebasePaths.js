/**
 * Debug utility to compare Firebase paths between listener and sender
 */

import { getSenderId, determineChatType, getCollectionPaths } from '../services/firebase/collectionPaths';

export const debugFirebasePaths = (selectedChat, selectedWhatsappChat, activeFilter, selectedPage) => {
    console.log(`[PATH_DEBUG] *** FIREBASE PATH DEBUGGING ***`);

    const currentChat = selectedChat || selectedWhatsappChat;
    if (!currentChat || !selectedPage?.page_id) {
        console.log(`[PATH_DEBUG] Missing requirements:`, {
            hasChat: !!currentChat,
            hasPage: !!selectedPage?.page_id
        });
        return;
    }

    // Determine chat type
    const chatType = selectedWhatsappChat ? 'whatsapp' :
        selectedChat?.flage === 'instagram' ? 'instagram' : 'messenger';

    console.log(`[PATH_DEBUG] Chat info:`, {
        chatType,
        activeFilter,
        chatId: currentChat.id,
        pageId: selectedPage.page_id
    });

    // Get sender ID (what listener uses)
    const senderId = getSenderId(currentChat, chatType);
    console.log(`[PATH_DEBUG] Sender ID from getSenderId:`, senderId);

    // Get collection paths (what sendMessage uses)
    const collectionPaths = getCollectionPaths(currentChat, chatType, selectedPage.page_id);
    console.log(`[PATH_DEBUG] Collection paths from getCollectionPaths:`, collectionPaths);

    // Manual path construction (what listener does)
    const listenerPath = `${selectedPage.page_id}/${senderId}/messages`;
    console.log(`[PATH_DEBUG] Listener path:`, listenerPath);

    // Compare
    const pathsMatch = listenerPath === collectionPaths.messages;
    console.log(`[PATH_DEBUG] Paths match:`, pathsMatch);

    if (!pathsMatch) {
        console.error(`[PATH_DEBUG] *** PATH MISMATCH DETECTED ***`);
        console.error(`[PATH_DEBUG] Listener path: ${listenerPath}`);
        console.error(`[PATH_DEBUG] SendMessage path: ${collectionPaths.messages}`);
    }

    return {
        chatType,
        senderId,
        listenerPath,
        sendMessagePath: collectionPaths.messages,
        pathsMatch
    };
};

// Make it available globally for testing
if (typeof window !== 'undefined') {
    window.debugFirebasePaths = debugFirebasePaths;
}
