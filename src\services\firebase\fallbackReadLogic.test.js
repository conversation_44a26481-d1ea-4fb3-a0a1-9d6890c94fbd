/**
 * Tests for fallback read logic and error scenarios in Firebase collection consistency
 */

import {
    collection,
    doc,
    getDocs,
    onSnapshot,
    query,
    orderBy
} from 'firebase/firestore';

// Mock Firebase Firestore
jest.mock('firebase/firestore', () => ({
    collection: jest.fn(),
    doc: jest.fn(),
    getDocs: jest.fn(),
    onSnapshot: jest.fn(),
    query: jest.fn(),
    orderBy: jest.fn()
}));

// Mock Firebase config
jest.mock('../../utils/firebase.config', () => ({
    db: {}
}));

// Mock collection paths service
jest.mock('./collectionPaths', () => ({
    getDualWritePaths: jest.fn(),
    determineChatType: jest.fn(),
    getChatIdentifier: jest.fn(),
    getCollectionPaths: jest.fn()
}));

import {
    getDualWritePaths,
    determineChatType,
    getChatIdentifier,
    getCollectionPaths
} from './collectionPaths';

describe('Fallback Read Logic and Error Scenarios', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    describe('Message Fallback Read Logic', () => {
        const mockSelectedChat = {
            id: 'backend_chat_123',
            participants: {
                data: [{ id: 'participant_456' }]
            }
        };

        const mockDualWritePaths = {
            current: {
                messages: 'chats/backend_chat_123/messages',
                comments: 'chats/backend_chat_123/comments'
            },
            legacy: {
                messages: 'chats/participant_456/messages',
                comments: 'chats/backend_chat_123/comments'
            },
            chatId: 'backend_chat_123',
            requiresMigration: true
        };

        beforeEach(() => {
            determineChatType.mockReturnValue('messenger');
            getDualWritePaths.mockReturnValue(mockDualWritePaths);
            getChatIdentifier.mockReturnValue('backend_chat_123');
        });

        test('reads from new path when it has messages', async () => {
            const mockNewPathMessages = [
                {
                    id: 'msg1',
                    data: () => ({
                        message: 'New path message',
                        sender: 'backend_chat_123',
                        created_time: '2024-01-01T10:00:00Z'
                    })
                }
            ];

            getDocs.mockResolvedValueOnce({ docs: mockNewPathMessages });
            collection.mockReturnValue({});
            query.mockReturnValue({});
            orderBy.mockReturnValue({});

            // Simulate the fallback read logic
            const readMessagesWithFallback = async (selectedChat) => {
                const chatType = determineChatType(selectedChat);
                const dualPaths = getDualWritePaths(selectedChat, chatType);

                try {
                    // Try new path first
                    const newPathRef = collection({}, ...dualPaths.current.messages.split('/'));
                    const newPathQuery = query(newPathRef, orderBy('created_time', 'asc'));
                    const newPathSnapshot = await getDocs(newPathQuery);

                    if (newPathSnapshot.docs.length > 0) {
                        return {
                            messages: newPathSnapshot.docs,
                            usedPath: dualPaths.current.messages,
                            fallbackUsed: false
                        };
                    }

                    // Fallback to legacy path
                    const legacyPathRef = collection({}, ...dualPaths.legacy.messages.split('/'));
                    const legacyPathQuery = query(legacyPathRef, orderBy('created_time', 'asc'));
                    const legacyPathSnapshot = await getDocs(legacyPathQuery);

                    return {
                        messages: legacyPathSnapshot.docs,
                        usedPath: dualPaths.legacy.messages,
                        fallbackUsed: true
                    };
                } catch (error) {
                    return {
                        messages: [],
                        usedPath: null,
                        fallbackUsed: false,
                        error: error.message
                    };
                }
            };

            const result = await readMessagesWithFallback(mockSelectedChat);

            expect(result.messages).toHaveLength(1);
            expect(result.usedPath).toBe('chats/backend_chat_123/messages');
            expect(result.fallbackUsed).toBe(false);
            expect(getDocs).toHaveBeenCalledTimes(1);
        });

        test('falls back to legacy path when new path is empty', async () => {
            const mockLegacyPathMessages = [
                {
                    id: 'msg1',
                    data: () => ({
                        message: 'Legacy path message',
                        sender: 'participant_456',
                        created_time: '2024-01-01T10:00:00Z'
                    })
                }
            ];

            getDocs.mockResolvedValueOnce({ docs: [] }) // New path empty
                .mockResolvedValueOnce({ docs: mockLegacyPathMessages }); // Legacy path has messages

            collection.mockReturnValue({});
            query.mockReturnValue({});
            orderBy.mockReturnValue({});

            const readMessagesWithFallback = async (selectedChat) => {
                const chatType = determineChatType(selectedChat);
                const dualPaths = getDualWritePaths(selectedChat, chatType);

                // Try new path first
                const newPathRef = collection({}, ...dualPaths.current.messages.split('/'));
                const newPathQuery = query(newPathRef, orderBy('created_time', 'asc'));
                const newPathSnapshot = await getDocs(newPathQuery);

                if (newPathSnapshot.docs.length > 0) {
                    return {
                        messages: newPathSnapshot.docs,
                        usedPath: dualPaths.current.messages,
                        fallbackUsed: false
                    };
                }

                // Fallback to legacy path
                const legacyPathRef = collection({}, ...dualPaths.legacy.messages.split('/'));
                const legacyPathQuery = query(legacyPathRef, orderBy('created_time', 'asc'));
                const legacyPathSnapshot = await getDocs(legacyPathQuery);

                return {
                    messages: legacyPathSnapshot.docs,
                    usedPath: dualPaths.legacy.messages,
                    fallbackUsed: true
                };
            };

            const result = await readMessagesWithFallback(mockSelectedChat);

            expect(result.messages).toHaveLength(1);
            expect(result.usedPath).toBe('chats/participant_456/messages');
            expect(result.fallbackUsed).toBe(true);
            expect(getDocs).toHaveBeenCalledTimes(2);
        });

        test('returns empty array when both paths are empty', async () => {
            getDocs.mockResolvedValue({ docs: [] }); // Both paths empty

            collection.mockReturnValue({});
            query.mockReturnValue({});
            orderBy.mockReturnValue({});

            const readMessagesWithFallback = async (selectedChat) => {
                const chatType = determineChatType(selectedChat);
                const dualPaths = getDualWritePaths(selectedChat, chatType);

                // Try new path first
                const newPathRef = collection({}, ...dualPaths.current.messages.split('/'));
                const newPathQuery = query(newPathRef, orderBy('created_time', 'asc'));
                const newPathSnapshot = await getDocs(newPathQuery);

                if (newPathSnapshot.docs.length > 0) {
                    return {
                        messages: newPathSnapshot.docs,
                        usedPath: dualPaths.current.messages,
                        fallbackUsed: false
                    };
                }

                // Fallback to legacy path
                const legacyPathRef = collection({}, ...dualPaths.legacy.messages.split('/'));
                const legacyPathQuery = query(legacyPathRef, orderBy('created_time', 'asc'));
                const legacyPathSnapshot = await getDocs(legacyPathQuery);

                return {
                    messages: legacyPathSnapshot.docs,
                    usedPath: dualPaths.legacy.messages,
                    fallbackUsed: true
                };
            };

            const result = await readMessagesWithFallback(mockSelectedChat);

            expect(result.messages).toHaveLength(0);
            expect(result.usedPath).toBe('chats/participant_456/messages');
            expect(result.fallbackUsed).toBe(true);
            expect(getDocs).toHaveBeenCalledTimes(2);
        });

        test('handles Firebase read errors gracefully', async () => {
            const firebaseError = new Error('Permission denied');
            getDocs.mockRejectedValue(firebaseError);

            collection.mockReturnValue({});
            query.mockReturnValue({});
            orderBy.mockReturnValue({});

            const readMessagesWithFallback = async (selectedChat) => {
                const chatType = determineChatType(selectedChat);
                const dualPaths = getDualWritePaths(selectedChat, chatType);

                try {
                    const newPathRef = collection({}, ...dualPaths.current.messages.split('/'));
                    const newPathQuery = query(newPathRef, orderBy('created_time', 'asc'));
                    const newPathSnapshot = await getDocs(newPathQuery);

                    return {
                        messages: newPathSnapshot.docs,
                        usedPath: dualPaths.current.messages,
                        fallbackUsed: false
                    };
                } catch (error) {
                    console.error('Error reading messages:', error);
                    return {
                        messages: [],
                        usedPath: null,
                        fallbackUsed: false,
                        error: error.message
                    };
                }
            };

            const result = await readMessagesWithFallback(mockSelectedChat);

            expect(result.messages).toHaveLength(0);
            expect(result.usedPath).toBeNull();
            expect(result.error).toBe('Permission denied');
        });

        test('handles partial failures in fallback chain', async () => {
            const firebaseError = new Error('Network error');
            const mockLegacyMessages = [
                {
                    id: 'msg1',
                    data: () => ({
                        message: 'Legacy message',
                        sender: 'participant_456',
                        created_time: '2024-01-01T10:00:00Z'
                    })
                }
            ];

            getDocs.mockRejectedValueOnce(firebaseError) // New path fails
                .mockResolvedValueOnce({ docs: mockLegacyMessages }); // Legacy path succeeds

            collection.mockReturnValue({});
            query.mockReturnValue({});
            orderBy.mockReturnValue({});

            const readMessagesWithFallback = async (selectedChat) => {
                const chatType = determineChatType(selectedChat);
                const dualPaths = getDualWritePaths(selectedChat, chatType);

                try {
                    // Try new path first
                    const newPathRef = collection({}, ...dualPaths.current.messages.split('/'));
                    const newPathQuery = query(newPathRef, orderBy('created_time', 'asc'));
                    const newPathSnapshot = await getDocs(newPathQuery);

                    return {
                        messages: newPathSnapshot.docs,
                        usedPath: dualPaths.current.messages,
                        fallbackUsed: false
                    };
                } catch (newPathError) {
                    console.warn('New path failed, trying legacy path:', newPathError);

                    try {
                        // Fallback to legacy path
                        const legacyPathRef = collection({}, ...dualPaths.legacy.messages.split('/'));
                        const legacyPathQuery = query(legacyPathRef, orderBy('created_time', 'asc'));
                        const legacyPathSnapshot = await getDocs(legacyPathQuery);

                        return {
                            messages: legacyPathSnapshot.docs,
                            usedPath: dualPaths.legacy.messages,
                            fallbackUsed: true,
                            newPathError: newPathError.message
                        };
                    } catch (legacyPathError) {
                        return {
                            messages: [],
                            usedPath: null,
                            fallbackUsed: false,
                            error: `Both paths failed: ${newPathError.message}, ${legacyPathError.message}`
                        };
                    }
                }
            };

            const result = await readMessagesWithFallback(mockSelectedChat);

            expect(result.messages).toHaveLength(1);
            expect(result.usedPath).toBe('chats/participant_456/messages');
            expect(result.fallbackUsed).toBe(true);
            expect(result.newPathError).toBe('Network error');
            expect(getDocs).toHaveBeenCalledTimes(2);
        });
    });

    describe('Real-time Listener Fallback', () => {
        const mockSelectedChat = {
            id: 'backend_chat_123',
            participants: {
                data: [{ id: 'participant_456' }]
            }
        };

        const mockDualWritePaths = {
            current: {
                messages: 'chats/backend_chat_123/messages'
            },
            legacy: {
                messages: 'chats/participant_456/messages'
            },
            chatId: 'backend_chat_123'
        };

        beforeEach(() => {
            determineChatType.mockReturnValue('messenger');
            getDualWritePaths.mockReturnValue(mockDualWritePaths);
        });

        test('sets up listener on new path when it has data', async () => {
            const mockMessages = [
                {
                    id: 'msg1',
                    data: () => ({
                        message: 'Real-time message',
                        created_time: '2024-01-01T10:00:00Z'
                    })
                }
            ];

            getDocs.mockResolvedValueOnce({ docs: mockMessages }); // New path has data

            const mockUnsubscribe = jest.fn();
            onSnapshot.mockImplementation((query, callback) => {
                callback({ docs: mockMessages });
                return mockUnsubscribe;
            });

            collection.mockReturnValue({});
            query.mockReturnValue({});
            orderBy.mockReturnValue({});

            const setupListenerWithFallback = async (selectedChat, onUpdate) => {
                const chatType = determineChatType(selectedChat);
                const dualPaths = getDualWritePaths(selectedChat, chatType);

                // Check which path has data
                const newPathRef = collection({}, ...dualPaths.current.messages.split('/'));
                const newPathQuery = query(newPathRef, orderBy('created_time', 'asc'));
                const newPathSnapshot = await getDocs(newPathQuery);

                let activeQuery;
                let activePath;

                if (newPathSnapshot.docs.length > 0) {
                    activeQuery = newPathQuery;
                    activePath = dualPaths.current.messages;
                } else {
                    const legacyPathRef = collection({}, ...dualPaths.legacy.messages.split('/'));
                    activeQuery = query(legacyPathRef, orderBy('created_time', 'asc'));
                    activePath = dualPaths.legacy.messages;
                }

                const unsubscribe = onSnapshot(activeQuery, onUpdate);
                return { unsubscribe, activePath };
            };

            const mockOnUpdate = jest.fn();
            const result = await setupListenerWithFallback(mockSelectedChat, mockOnUpdate);

            expect(result.activePath).toBe('chats/backend_chat_123/messages');
            expect(onSnapshot).toHaveBeenCalledTimes(1);
            expect(mockOnUpdate).toHaveBeenCalledWith({ docs: mockMessages });
        });

        test('sets up listener on legacy path when new path is empty', async () => {
            const mockLegacyMessages = [
                {
                    id: 'msg1',
                    data: () => ({
                        message: 'Legacy real-time message',
                        created_time: '2024-01-01T10:00:00Z'
                    })
                }
            ];

            getDocs.mockResolvedValueOnce({ docs: [] }) // New path empty
                .mockResolvedValueOnce({ docs: mockLegacyMessages }); // Legacy path has data

            const mockUnsubscribe = jest.fn();
            onSnapshot.mockImplementation((query, callback) => {
                callback({ docs: mockLegacyMessages });
                return mockUnsubscribe;
            });

            collection.mockReturnValue({});
            query.mockReturnValue({});
            orderBy.mockReturnValue({});

            const setupListenerWithFallback = async (selectedChat, onUpdate) => {
                const chatType = determineChatType(selectedChat);
                const dualPaths = getDualWritePaths(selectedChat, chatType);

                // Check which path has data
                const newPathRef = collection({}, ...dualPaths.current.messages.split('/'));
                const newPathQuery = query(newPathRef, orderBy('created_time', 'asc'));
                const newPathSnapshot = await getDocs(newPathQuery);

                let activeQuery;
                let activePath;

                if (newPathSnapshot.docs.length > 0) {
                    activeQuery = newPathQuery;
                    activePath = dualPaths.current.messages;
                } else {
                    const legacyPathRef = collection({}, ...dualPaths.legacy.messages.split('/'));
                    const legacyPathQuery = query(legacyPathRef, orderBy('created_time', 'asc'));
                    activeQuery = legacyPathQuery;
                    activePath = dualPaths.legacy.messages;
                }

                const unsubscribe = onSnapshot(activeQuery, onUpdate);
                return { unsubscribe, activePath };
            };

            const mockOnUpdate = jest.fn();
            const result = await setupListenerWithFallback(mockSelectedChat, mockOnUpdate);

            expect(result.activePath).toBe('chats/participant_456/messages');
            expect(onSnapshot).toHaveBeenCalledTimes(1);
            expect(mockOnUpdate).toHaveBeenCalledWith({ docs: mockLegacyMessages });
            expect(getDocs).toHaveBeenCalledTimes(2);
        });

        test('handles listener errors gracefully', async () => {
            const listenerError = new Error('Listener permission denied');

            getDocs.mockResolvedValue({ docs: [] });
            onSnapshot.mockImplementation((query, callback, errorCallback) => {
                errorCallback(listenerError);
                return jest.fn();
            });

            collection.mockReturnValue({});
            query.mockReturnValue({});
            orderBy.mockReturnValue({});

            const setupListenerWithFallback = async (selectedChat, onUpdate, onError) => {
                const chatType = determineChatType(selectedChat);
                const dualPaths = getDualWritePaths(selectedChat, chatType);

                try {
                    const newPathRef = collection({}, ...dualPaths.current.messages.split('/'));
                    const newPathQuery = query(newPathRef, orderBy('created_time', 'asc'));

                    const unsubscribe = onSnapshot(
                        newPathQuery,
                        onUpdate,
                        (error) => {
                            console.error('Listener error:', error);
                            if (onError) onError(error);
                        }
                    );

                    return { unsubscribe, activePath: dualPaths.current.messages };
                } catch (error) {
                    if (onError) onError(error);
                    return { unsubscribe: null, activePath: null, error: error.message };
                }
            };

            const mockOnUpdate = jest.fn();
            const mockOnError = jest.fn();
            const result = await setupListenerWithFallback(mockSelectedChat, mockOnUpdate, mockOnError);

            expect(result.activePath).toBe('chats/backend_chat_123/messages');
            expect(mockOnError).toHaveBeenCalledWith(listenerError);
        });
    });

    describe('WhatsApp Fallback Scenarios', () => {
        const mockWhatsAppChat = {
            sender_phone_number: '+1234567890'
        };

        beforeEach(() => {
            determineChatType.mockReturnValue('whatsapp');
            getChatIdentifier.mockReturnValue('1234567890');
            getCollectionPaths.mockReturnValue({
                messages: 'whatsApp/1234567890/messages',
                comments: 'whatsApp/1234567890/comments'
            });
        });

        test('WhatsApp does not require fallback (already consistent)', async () => {
            const mockMessages = [
                {
                    id: 'msg1',
                    data: () => ({
                        message: 'WhatsApp message',
                        sender: '1234567890',
                        created_time: '2024-01-01T10:00:00Z'
                    })
                }
            ];

            getDocs.mockResolvedValue({ docs: mockMessages });
            collection.mockReturnValue({});
            query.mockReturnValue({});
            orderBy.mockReturnValue({});

            const readWhatsAppMessages = async (selectedChat) => {
                const chatType = determineChatType(selectedChat);
                const chatId = getChatIdentifier(selectedChat, chatType);
                const { messages: messagesPath } = getCollectionPaths(chatId, chatType, selectedChat);

                const messagesRef = collection({}, ...messagesPath.split('/'));
                const messagesQuery = query(messagesRef, orderBy('created_time', 'asc'));
                const snapshot = await getDocs(messagesQuery);

                return {
                    messages: snapshot.docs,
                    path: messagesPath,
                    requiresFallback: false
                };
            };

            const result = await readWhatsAppMessages(mockWhatsAppChat);

            expect(result.messages).toHaveLength(1);
            expect(result.path).toBe('whatsApp/1234567890/messages');
            expect(result.requiresFallback).toBe(false);
            expect(getDocs).toHaveBeenCalledTimes(1);
        });

        test('handles WhatsApp phone number normalization errors', async () => {
            const malformedChat = {
                sender_phone_number: null
            };

            getChatIdentifier.mockReturnValue(null);

            const readWhatsAppMessages = async (selectedChat) => {
                try {
                    const chatType = determineChatType(selectedChat);
                    const chatId = getChatIdentifier(selectedChat, chatType);

                    if (!chatId) {
                        throw new Error('Unable to determine chat identifier');
                    }

                    const { messages: messagesPath } = getCollectionPaths(chatId, chatType, selectedChat);
                    // ... rest of the logic
                } catch (error) {
                    return {
                        messages: [],
                        path: null,
                        error: error.message
                    };
                }
            };

            const result = await readWhatsAppMessages(malformedChat);

            expect(result.messages).toHaveLength(0);
            expect(result.path).toBeNull();
            expect(result.error).toBe('Unable to determine chat identifier');
        });
    });

    describe('Error Recovery Strategies', () => {
        test('implements exponential backoff for transient errors', async () => {
            const transientError = new Error('Temporary network error');
            let attemptCount = 0;

            getDocs.mockImplementation(() => {
                attemptCount++;
                if (attemptCount < 3) {
                    return Promise.reject(transientError);
                }
                return Promise.resolve({ docs: [] });
            });

            collection.mockReturnValue({});
            query.mockReturnValue({});
            orderBy.mockReturnValue({});

            const readWithRetry = async (selectedChat, maxRetries = 3) => {
                const chatType = determineChatType(selectedChat);
                const dualPaths = getDualWritePaths(selectedChat, chatType);

                for (let attempt = 1; attempt <= maxRetries; attempt++) {
                    try {
                        const pathRef = collection({}, ...dualPaths.current.messages.split('/'));
                        const pathQuery = query(pathRef, orderBy('created_time', 'asc'));
                        const snapshot = await getDocs(pathQuery);

                        return {
                            messages: snapshot.docs,
                            attempt,
                            success: true
                        };
                    } catch (error) {
                        if (attempt === maxRetries) {
                            return {
                                messages: [],
                                attempt,
                                success: false,
                                error: error.message
                            };
                        }

                        // Exponential backoff
                        const delay = Math.pow(2, attempt - 1) * 100;
                        await new Promise(resolve => setTimeout(resolve, delay));
                    }
                }
            };

            const mockSelectedChat = {
                id: 'backend_chat_123',
                participants: { data: [{ id: 'participant_456' }] }
            };

            determineChatType.mockReturnValue('messenger');
            getDualWritePaths.mockReturnValue({
                current: { messages: 'chats/backend_chat_123/messages' },
                legacy: { messages: 'chats/participant_456/messages' }
            });

            const result = await readWithRetry(mockSelectedChat);

            expect(result.success).toBe(true);
            expect(result.attempt).toBe(3);
            expect(getDocs).toHaveBeenCalledTimes(3);
        });

        test('implements circuit breaker pattern for persistent errors', async () => {
            const persistentError = new Error('Service unavailable');
            let circuitOpen = false;
            let failureCount = 0;

            getDocs.mockImplementation(() => {
                if (circuitOpen) {
                    throw new Error('Circuit breaker open');
                }
                failureCount++;
                if (failureCount >= 3) {
                    circuitOpen = true;
                }
                return Promise.reject(persistentError);
            });

            collection.mockReturnValue({});
            query.mockReturnValue({});
            orderBy.mockReturnValue({});

            const readWithCircuitBreaker = async (selectedChat) => {
                if (circuitOpen) {
                    return {
                        messages: [],
                        circuitOpen: true,
                        error: 'Circuit breaker open - service temporarily unavailable'
                    };
                }

                try {
                    const chatType = determineChatType(selectedChat);
                    const dualPaths = getDualWritePaths(selectedChat, chatType);

                    const pathRef = collection({}, ...dualPaths.current.messages.split('/'));
                    const pathQuery = query(pathRef, orderBy('created_time', 'asc'));
                    const snapshot = await getDocs(pathQuery);

                    // Reset failure count on success
                    failureCount = 0;
                    circuitOpen = false;

                    return {
                        messages: snapshot.docs,
                        circuitOpen: false,
                        success: true
                    };
                } catch (error) {
                    return {
                        messages: [],
                        circuitOpen,
                        error: error.message,
                        failureCount
                    };
                }
            };

            const mockSelectedChat = {
                id: 'backend_chat_123',
                participants: { data: [{ id: 'participant_456' }] }
            };

            determineChatType.mockReturnValue('messenger');
            getDualWritePaths.mockReturnValue({
                current: { messages: 'chats/backend_chat_123/messages' },
                legacy: { messages: 'chats/participant_456/messages' }
            });

            // First few calls should fail normally
            let result1 = await readWithCircuitBreaker(mockSelectedChat);
            expect(result1.circuitOpen).toBe(false);
            expect(result1.failureCount).toBe(1);

            let result2 = await readWithCircuitBreaker(mockSelectedChat);
            expect(result2.circuitOpen).toBe(false);
            expect(result2.failureCount).toBe(2);

            let result3 = await readWithCircuitBreaker(mockSelectedChat);
            expect(result3.circuitOpen).toBe(true);
            expect(result3.failureCount).toBe(3);

            // Subsequent calls should be blocked by circuit breaker
            let result4 = await readWithCircuitBreaker(mockSelectedChat);
            expect(result4.circuitOpen).toBe(true);
            expect(result4.error).toBe('Circuit breaker open - service temporarily unavailable');
        });
    });
});
