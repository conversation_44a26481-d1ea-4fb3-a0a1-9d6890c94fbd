import apiRequest from "../../utils/apiRequest";
import { shouldShowPackageFeatures } from "../../config/packageVisibility";

const getAllPackagesApi = async (userId = null) => {
    // Check if user should have access to package features
    if (userId && !shouldShowPackageFeatures(userId)) {
        throw new Error('Package features are not available for this user');
    }

    return await apiRequest('allpackages', 'get');
};

export default getAllPackagesApi;
