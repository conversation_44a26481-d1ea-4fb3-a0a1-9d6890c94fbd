import { useEffect, useRef, useState } from "react";
import Message from "./Message";
import SendMessage from "./SendMessage";
import Cookies from "js-cookie";
import { Link } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { FaCloudUploadAlt } from "react-icons/fa";
import "./ChatBox.css";
import FetchingDataLoading from "../../components/LoadingAnimation/FetchingDataLoading";
import useForceRerender from "../../hooks/useForceRerender";

// Import from metaChatSlice
import {
  selectMessages,
  selectSelectedChat,
  selectSelectedWhatsappChat,
  selectDisabledChat,
  selectDisabledChatLimit,
  selectActiveFilter,
  setDisabledChat,
  selectWhatsappChatMessages,
} from "../../redux/features/metaBusinessChatSlice";
import { useSelector, useDispatch } from "react-redux";

const ChatBox = () => {
  const dispatch = useDispatch();

  // Get state from Redux - metaChatSlice
  const messages = useSelector(selectMessages);
  const nestedMessages = useSelector(
    (state) => state.metaBusinessSuite.nestedMessages
  );
  const selectedChat = useSelector(selectSelectedChat);
  const disabledChat = useSelector(selectDisabledChat);
  const disabledChatLimit = useSelector(selectDisabledChatLimit);
  const activeFilter = useSelector(selectActiveFilter);
  const whatsappChatMessages = useSelector(selectWhatsappChatMessages);
  const selectedWhatsappChat = useSelector(selectSelectedWhatsappChat);

  // Also get raw messages directly from state for comparison
  const rawMessages = useSelector(state => state.metaBusinessSuite.messages);
  const rawWhatsappMessages = useSelector(state => state.metaBusinessSuite.whatsappChatMessages);
  const rawNestedMessages = useSelector(state => state.metaBusinessSuite.nestedMessages);

  const messagesEndRef = useRef(null);
  const [sendingMessages, setSendingMessages] = useState([]);
  const [isDragging, setIsDragging] = useState(false);
  const fileInputRef = useRef(null);
  const forceRerender = useForceRerender();

  // Get user data from Redux auth state
  const user = useSelector((state) => state.auth.user);

  const pageAvatarFromCookie = Cookies.get("page_img");
  const pageAvatar = pageAvatarFromCookie || "dummy";

  // Get user avatar from Redux state, construct full URL if photo exists
  const userAvatar = activeFilter === "instagram" ? selectedChat?.participants?.data[1]?.image : selectedChat?.participants?.data[0]?.image || "dummy";

  // Loading states for messages (Messenger/Instagram & WhatsApp)
  const fetchingMessages = useSelector(
    (state) => state.metaBusinessSuite.loading.fetchingMessages
  );
  const fetchingWhatsAppMessages = useSelector(
    (state) => state.metaBusinessSuite.loading.fetchingWhatsAppMessages
  );
  const loadingMessages = fetchingMessages || fetchingWhatsAppMessages;

  // Function to scroll to the bottom of the chat
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  // Scroll to bottom when messages change
  useEffect(() => {
    scrollToBottom();
  }, [messages, whatsappChatMessages, sendingMessages]);

  // Debug effect to track message updates
  useEffect(() => {
    console.log('[CHATBOX_DEBUG] Messages updated:', {
      activeFilter,
      messagesCount: messages?.length || 0,
      whatsappMessagesCount: whatsappChatMessages?.length || 0,
      sendingMessagesCount: sendingMessages?.length || 0,
      rawMessagesCount: rawMessages?.length || 0,
      rawWhatsappMessagesCount: rawWhatsappMessages?.length || 0,
      rawNestedMessagesCount: rawNestedMessages?.length || 0,
      timestamp: new Date().toISOString()
    });
  }, [messages, whatsappChatMessages, sendingMessages, activeFilter, rawMessages, rawWhatsappMessages, rawNestedMessages]);

  // Listen for custom events from Firebase listeners to force re-render
  useEffect(() => {
    const handleMessagesUpdated = (event) => {
      console.log('[CHATBOX_DEBUG] Custom messagesUpdated event received:', event.detail);
      forceRerender();
    };

    window.addEventListener('messagesUpdated', handleMessagesUpdated);

    return () => {
      window.removeEventListener('messagesUpdated', handleMessagesUpdated);
    };
  }, [forceRerender]);

  useEffect(() => {
    return () => {
      dispatch(setDisabledChat(false));
    };
  }, [dispatch]);

  // Function to add a temporary sending message
  const addSendingMessage = (message) => {
    setSendingMessages((prev) => [...prev, message]);
  };

  // Function to remove a sending message when it's done
  const removeSendingMessage = (id) => {
    setSendingMessages((prev) => prev.filter((msg) => msg.id !== id));
  };

  const { t } = useTranslation();

  // Drag and drop handlers
  const handleDragEnter = (e) => {
    e.preventDefault();
    e.stopPropagation();
    if (!selectedChat || disabledChat || disabledChatLimit) return;
    setIsDragging(true);
  };

  const handleDragOver = (e) => {
    e.preventDefault();
    e.stopPropagation();
    if (!selectedChat || disabledChat || disabledChatLimit) return;
    if (!isDragging) setIsDragging(true);
  };

  const handleDragLeave = (e) => {
    e.preventDefault();
    e.stopPropagation();
    // Only set isDragging to false if we're leaving the main container
    // and not just moving between child elements
    if (e.currentTarget.contains(e.relatedTarget)) return;
    setIsDragging(false);
  };

  const handleDrop = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);

    if (!selectedChat || disabledChat || disabledChatLimit) return;

    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
      // Pass the first file to the file input in SendMessage component
      if (fileInputRef.current && fileInputRef.current.handleFileUpload) {
        fileInputRef.current.handleFileUpload(e.dataTransfer.files[0]);
      }
    }
  };

  return (
    <main
      className="chat-box"
      onDragEnter={handleDragEnter}
      onDragOver={handleDragOver}
      onDragLeave={handleDragLeave}
      onDrop={handleDrop}
    >
      {loadingMessages && (
        <div className="loading-overlay">
          <FetchingDataLoading />
        </div>
      )}
      {isDragging && (
        <div className="drag-overlay">
          <div className="drag-overlay-content">
            <FaCloudUploadAlt size={50} />
            <p>Drop file to send</p>
          </div>
        </div>
      )}
      <div className="messages-wrapper">
        {!loadingMessages && (
          <>
            {activeFilter === "whatsapp"
              ? whatsappChatMessages?.map((message, index) => {
                  return (
                    <Message
                      key={message?.sender + index}
                      message={message}
                      userAvatar={userAvatar}
                      pageAvatar={pageAvatar}
                      isSending={false}
                    />
                  );
                })
              : messages?.map((message, index) => (
                  <Message
                    key={message?.from?.id?.toString() + index}
                    message={message}
                    userAvatar={userAvatar}
                    pageAvatar={pageAvatar}
                    isSending={false}
                  />
                ))}

            {/* Render sending messages with loading state */}
            {sendingMessages.map((message, index) => (
              <Message
                key={`sending-${message.id}-${index}`}
                message={message}
                userAvatar={userAvatar}
                pageAvatar={pageAvatar}
                isSending={true}
              />
            ))}

            {disabledChat ? (
              <p className={"text-muted content-container"}>
                We're sorry, but you can't send message to this chat right now
                as it has been more than 24 hours since your last interaction.
              </p>
            ) : disabledChatLimit ? (
              <>
                <div className="quota-container">
                  Limit Exceeded for today, upgrade your plan to continue.
                  <Link to={"/packages"}>
                    <button className="gradient-border">Upgrade Now</button>{" "}
                  </Link>
                </div>
              </>
            ) : null}
          </>
        )}
        <div ref={messagesEndRef} />
      </div>
      {((selectedChat && activeFilter !== "whatsapp") ||
        (selectedWhatsappChat && activeFilter === "whatsapp")) &&
      !disabledChat &&
      !disabledChatLimit ? (
        <SendMessage
          addSendingMessage={addSendingMessage}
          removeSendingMessage={removeSendingMessage}
          ref={fileInputRef}
        />
      ) : null}
    </main>
  );
};

export default ChatBox;
