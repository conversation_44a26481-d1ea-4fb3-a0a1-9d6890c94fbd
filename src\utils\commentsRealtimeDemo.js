/**
 * Demonstration of the real-time comments functionality
 * This file shows how to use the new real-time features
 */

import { store } from '../redux/store';
import {
    startCommentsListener,
    stopCommentsListener,
    addComment,
    markCommentsAsReadAsync,
    fetchComments,
    updateCommentsRealtime
} from '../redux/features/metaBusinessChatSlice';

/**
 * Example usage of the real-time comments system
 */
export class CommentsRealtimeDemo {
    constructor() {
        this.currentUserId = 'demo-user-1';
        this.currentUser = {
            id: 'demo-user-1',
            name: 'Demo User',
            email: '<EMAIL>'
        };
    }

    /**
     * Start listening to comments for a specific chat
     * @param {string} chatId - The chat identifier
     * @param {string} chatType - The type of chat ('whatsapp', 'messenger', 'instagram')
     */
    async startListening(chatId, chatType) {
        console.log(`Starting real-time listener for ${chatType} chat: ${chatId}`);

        try {
            // Start the real-time listener
            await store.dispatch(startCommentsListener({
                chatId,
                chatType,
                currentUserId: this.currentUserId
            }));

            console.log('Real-time listener started successfully');

            // Subscribe to state changes to see real-time updates
            const unsubscribe = store.subscribe(() => {
                const state = store.getState().metaBusinessSuite;
                console.log('Comments updated:', {
                    count: state.comments.length,
                    unreadCount: state.commentsModal.unreadCount,
                    loading: state.commentsModal.loading
                });
            });

            return unsubscribe;
        } catch (error) {
            console.error('Failed to start comments listener:', error);
            throw error;
        }
    }

    /**
     * Stop listening to comments and cleanup
     */
    async stopListening() {
        console.log('Stopping real-time listener');

        try {
            await store.dispatch(stopCommentsListener());
            console.log('Real-time listener stopped successfully');
        } catch (error) {
            console.error('Failed to stop comments listener:', error);
            throw error;
        }
    }

    /**
     * Add a new comment
     * @param {string} chatId - The chat identifier
     * @param {string} chatType - The type of chat
     * @param {string} text - The comment text
     */
    async addComment(chatId, chatType, text) {
        console.log(`Adding comment to ${chatType} chat ${chatId}: "${text}"`);

        try {
            await store.dispatch(addComment({
                chatId,
                chatType,
                text,
                author: this.currentUser
            }));

            console.log('Comment added successfully');
        } catch (error) {
            console.error('Failed to add comment:', error);
            throw error;
        }
    }

    /**
     * Mark comments as read
     * @param {string} chatId - The chat identifier
     * @param {string} chatType - The type of chat
     * @param {Array<string>} commentIds - Array of comment IDs to mark as read
     */
    async markAsRead(chatId, chatType, commentIds) {
        console.log(`Marking ${commentIds.length} comments as read`);

        try {
            await store.dispatch(markCommentsAsReadAsync({
                chatId,
                chatType,
                user: this.currentUser,
                commentIds
            }));

            console.log('Comments marked as read successfully');
        } catch (error) {
            console.error('Failed to mark comments as read:', error);
            throw error;
        }
    }

    /**
     * Fetch existing comments (one-time fetch)
     * @param {string} chatId - The chat identifier
     * @param {string} chatType - The type of chat
     */
    async fetchComments(chatId, chatType) {
        console.log(`Fetching existing comments for ${chatType} chat: ${chatId}`);

        try {
            await store.dispatch(fetchComments({ chatId, chatType }));

            const state = store.getState().metaBusinessSuite;
            console.log(`Fetched ${state.comments.length} comments`);

            return state.comments;
        } catch (error) {
            console.error('Failed to fetch comments:', error);
            throw error;
        }
    }

    /**
     * Get current comments state
     */
    getCurrentState() {
        const state = store.getState().metaBusinessSuite;
        return {
            comments: state.comments,
            unreadCount: state.commentsModal.unreadCount,
            loading: state.commentsModal.loading,
            modalOpen: state.commentsModal.isOpen,
            hasListener: !!state.commentsListeners.unsubscribe
        };
    }

    /**
     * Simulate real-time comment synchronization
     * This demonstrates how comments automatically sync across users
     */
    async simulateRealtimeSync(chatId, chatType) {
        console.log('Simulating real-time comment synchronization...');

        // Start listening
        const unsubscribe = await this.startListening(chatId, chatType);

        // Simulate adding comments from different users
        setTimeout(async () => {
            await this.addComment(chatId, chatType, 'Hello from User 1!');
        }, 1000);

        setTimeout(async () => {
            // Simulate comment from another user (would normally come through Firebase)
            const mockComment = {
                id: 'mock-comment-2',
                text: 'Hello from User 2!',
                author: { id: 'user-2', name: 'User 2', email: '<EMAIL>' },
                createdAt: new Date().toISOString(),
                readBy: [],
                chatType
            };

            // This would normally be triggered by the Firebase listener
            store.dispatch(updateCommentsRealtime({
                comments: [
                    ...store.getState().metaBusinessSuite.comments,
                    mockComment
                ],
                currentUserId: this.currentUserId
            }));
        }, 2000);

        // Mark comments as read after 3 seconds
        setTimeout(async () => {
            const state = store.getState().metaBusinessSuite;
            const commentIds = state.comments.map(comment => comment.id);
            await this.markAsRead(chatId, chatType, commentIds);
        }, 3000);

        // Stop listening after 5 seconds
        setTimeout(async () => {
            unsubscribe();
            await this.stopListening();
            console.log('Demo completed');
        }, 5000);
    }
}

/**
 * Example usage:
 *
 * const demo = new CommentsRealtimeDemo();
 *
 * // Start listening to WhatsApp chat comments
 * demo.startListening('+1234567890', 'whatsapp');
 *
 * // Add a comment
 * demo.addComment('+1234567890', 'whatsapp', 'This is a test comment');
 *
 * // Mark comments as read
 * demo.markAsRead('+1234567890', 'whatsapp', ['comment-id-1', 'comment-id-2']);
 *
 * // Stop listening
 * demo.stopListening();
 *
 * // Run full simulation
 * demo.simulateRealtimeSync('+1234567890', 'whatsapp');
 */

export default CommentsRealtimeDemo;
