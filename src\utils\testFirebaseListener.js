/**
 * Utility to test Firebase listeners by manually adding a test message
 * This is for debugging purposes only
 */

import { db } from './firebase.config';
import { collection, addDoc, serverTimestamp } from 'firebase/firestore';

export const addTestMessage = async (pageId, senderId) => {
    try {
        const messagesPath = `${pageId}/${senderId}/messages`;
        console.log(`[TEST] Adding test message to path: ${messagesPath}`);

        const testMessage = {
            id: `test_${Date.now()}`,
            message: `Test message at ${new Date().toISOString()}`,
            sender: senderId,
            created_time: new Date().toISOString(),
            message_type: 'text',
            from_user: true,
            _test: true // Mark as test message
        };

        const docRef = await addDoc(collection(db, ...messagesPath.split('/')), testMessage);
        console.log(`[TEST] Test message added with ID: ${docRef.id}`);

        return docRef.id;
    } catch (error) {
        console.error(`[TEST] Error adding test message:`, error);
        throw error;
    }
};

// Make it available globally for testing in browser console
if (typeof window !== 'undefined') {
    window.addTestMessage = addTestMessage;
}
