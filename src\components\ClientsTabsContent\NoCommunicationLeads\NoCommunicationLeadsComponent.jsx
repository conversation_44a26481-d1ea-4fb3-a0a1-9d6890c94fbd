import { useCallback, useState, useEffect } from "react";
import { Container } from "react-bootstrap";
import { useSelector, useDispatch } from "react-redux";
import leadService from "../../../services/leads";
import TableControllers from "../../CustomDataTable/TableControllers";
import PaginationComponent from "../../CustomDataTable/PaginationComponent";
import FilterTableNoCommLeadsComponent from "./FilterTableNoCommLeadsComponent";
import {
  setTotalPages,
  setCurrentPage,
  setRecordsPerPage,
  setTotal,
  setPaginationLinks,
} from "../../../redux/features/leadsPaginationSlice";
import {
  setNoCommunicationLeads,
  setLeadStatusCounts,
} from "../../../redux/features/clientSlice";
import { showErrorToast } from "../../../utils/toast-success-error";
import "../AllClients/Clients.css";
import "../../CustomDataTable/datatable.css";
import useClient from "../../../redux/hooks/useClient";
import FetchingDataLoading from "../../LoadingAnimation/FetchingDataLoading";
import { resetPagination } from "../../../redux/features/leadsPaginationSlice";

// Component to list leads that have no communication
const NoCommunicationLeadsComponent = () => {
  const dispatch = useDispatch();

  // Redux selectors
  const { currentPage, recordsPerPage } = useSelector(
    (state) => state.leadsPagination
  );

  const { noCommunicationLeads } = useClient();

  const [selectedSourceLocal, setSelectedSourceLocal] = useState(null);
  const [filterStatusLocal, setFilterStatusLocal] = useState("all");

  const [loading, setLoading] = useState(true);
  const [isSearchActive, setIsSearchActive] = useState(false);
  const [dataFetched, setDataFetched] = useState(false);

  // Add a new state for search term
  const [searchTerm, setSearchTerm] = useState("");
  // Initial fetch and on pagination/filter change
  useEffect(() => {
    dispatch(resetPagination());
  }, [dispatch]);

  const loadData = useCallback(async () => {
    try {
      setLoading(true);

      const leadsData = await leadService.getNoCommunicationLeadsApi(
        undefined,
        recordsPerPage,
        currentPage,
        selectedSourceLocal,
        filterStatusLocal !== "all" ? filterStatusLocal : null,
        searchTerm
      );

      if (leadsData && leadsData.data) {
        const leadsArray = leadsData.data["All Leads"] || [];
        const pageNumber = leadsData.data["Page Number"] || 1;
        const numberOfPages = leadsData.data["Number Of Pages"] || 1;

        const {
          inprogress = 0,
          pendding = 0,
          rejected = 0,
          assigned = 0,
          wrong_lead = 0,
          not_qualified = 0,
          no_communication = 0,
          canceled = 0,
          booked = 0,
        } = leadsData.data;

        dispatch(
          setLeadStatusCounts({
            assigned,
            inprogress,
            pendding,
            rejected,
            wrong_lead,
            not_qualified,
            no_communication,
            canceled,
            booked,
          })
        );
        dispatch(setNoCommunicationLeads(leadsArray));
        dispatch(setTotalPages(numberOfPages));
        dispatch(setCurrentPage(pageNumber));
        dispatch(setRecordsPerPage(recordsPerPage));
        dispatch(setTotal(leadsArray.length * numberOfPages));
        dispatch(setPaginationLinks([]));
        setDataFetched(true);
      }
    } catch (error) {
      showErrorToast(error.response?.data?.message || "An error occurred");
    } finally {
      setLoading(false);
    }
  }, [
    dispatch,
    recordsPerPage,
    currentPage,
    selectedSourceLocal,
    filterStatusLocal,
    searchTerm,
  ]);

  // useEffect to trigger data loading when dependencies change
  useEffect(() => {
    loadData();
  }, [
    currentPage,
    recordsPerPage,
    selectedSourceLocal,
    filterStatusLocal,
    searchTerm,
  ]);

  // Create a simple fetchData function that triggers a re-fetch
  const fetchData = useCallback(() => {
    loadData();
  }, [loadData]);

  // handle filter leads (search term). We'll reuse AllLeads logic simplified
  const handleFilterLeads = async (term) => {
    // Prevent multiple simultaneous requests
    if (loading) return;

    try {
      setLoading(true);
      setSearchTerm(term); // update search term state

      if (!term || term.length === 0) {
        setIsSearchActive(false);
        setSearchTerm(""); // This will trigger the useEffect to re-fetch
        return;
      }

      setIsSearchActive(true);
      // Don't reset other filters, just update searchTerm
      const leadsData = await leadService.getNoCommunicationLeadsApi(
        undefined,
        recordsPerPage,
        currentPage,
        selectedSourceLocal,
        filterStatusLocal !== "all" ? filterStatusLocal : null,
        term // search
      );
      if (leadsData && leadsData.data) {
        const leadsArray = leadsData.data["All Leads"] || [];
        const pageNumber = leadsData.data["Page Number"] || 1;
        const numberOfPages = leadsData.data["Number Of Pages"] || 1;
        // Extract status counts from response (if present)
        const {
          assigned = 0,
          inprogress = 0,
          pendding = 0,
          rejected = 0,
          wrong_lead = 0,
          not_qualified = 0,
          no_communication = 0,
          canceled = 0,
          booked = 0,
        } = leadsData.data;
        dispatch(
          setLeadStatusCounts({
            assigned,
            inprogress,
            pendding,
            rejected,
            wrong_lead,
            not_qualified,
            no_communication,
            canceled,
            booked,
          })
        );
        dispatch(setNoCommunicationLeads(leadsArray));
        dispatch(setTotalPages(numberOfPages));
        dispatch(setCurrentPage(pageNumber));
        dispatch(setRecordsPerPage(recordsPerPage));
        dispatch(setTotal(leadsArray.length * numberOfPages));
        dispatch(setPaginationLinks([]));
        setDataFetched(true);
      }
    } catch (error) {
      console.error("Error fetching leads:", error);
      showErrorToast(error.response?.data?.message || "An error occurred");
    } finally {
      setLoading(false);
    }
  };

  // Add status filter handler
  const handleFilterStatus = useCallback((status) => {
    setFilterStatusLocal(status);
    setIsSearchActive(false);
    // The useEffect will automatically handle the data fetching
  }, []);

  // Add source filter handler
  const handleSourceFilter = useCallback((source) => {
    setSelectedSourceLocal(source);
    setIsSearchActive(false);
    // The useEffect will automatically handle the data fetching
  }, []);

  const handleResetAllFilters = useCallback(() => {
    setSelectedSourceLocal(null);
    setFilterStatusLocal("all");
    setSearchTerm("");
    setIsSearchActive(false);
  }, []);

  return (
    <Container className="all-leads-table p-4">
      {/* Show loading animation only for initial fetch */}
      {loading && !dataFetched ? (
        <FetchingDataLoading />
      ) : (
        <>
          <TableControllers
            minimal
            setLoading={setLoading}
            loading={loading}
            abortController={undefined}
            fetchData={fetchData}
            handleFilterLeads={handleFilterLeads}
            handleSourceFilter={handleSourceFilter}
            handleFilterStatus={handleFilterStatus}
            setAbortController={() => {}}
            isSearchActive={isSearchActive}
            nocommunication={true}
            onClearAllFilters={handleResetAllFilters}
          />
          <FilterTableNoCommLeadsComponent
            loading={loading}
            data={noCommunicationLeads}
            hideNoData={!dataFetched}
          />
          {/* Pagination visible always */}
          <PaginationComponent />
        </>
      )}
    </Container>
  );
};

export default NoCommunicationLeadsComponent;
