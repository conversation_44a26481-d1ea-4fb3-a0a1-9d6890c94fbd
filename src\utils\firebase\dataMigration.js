/**
 * Firebase Data Migration Utilities
 *
 * This module provides utilities for migrating message data from sender ID-based
 * collection paths to chat ID-based collection paths for consistency with the comment system.
 */

import {
    collection,
    doc,
    getDocs,
    setDoc,
    deleteDoc,
    query,
    orderBy,
    writeBatch,
    getDoc
} from 'firebase/firestore';
import { db } from '../firebase.config';
import {
    getDualWritePaths,
    determineChatType,
    getChatIdentifier,
    requiresMigration
} from '../../services/firebase/collectionPaths';
import {
    logMigrationOperation,
    withPerformanceMonitoring,
    LOG_LEVELS,
    OPERATION_TYPES
} from './migrationMonitoring';

/**
 * Migration result object structure
 * @typedef {Object} MigrationResult
 * @property {boolean} success - Whether the migration was successful
 * @property {number} migratedCount - Number of messages migrated
 * @property {number} skippedCount - Number of messages skipped
 * @property {number} errorCount - Number of messages that failed to migrate
 * @property {Array} errors - Array of error objects
 * @property {string} fromPath - Source collection path
 * @property {string} toPath - Destination collection path
 */

/**
 * Migrate messages for a specific chat from sender ID to chat ID collection paths
 * @param {Object} selectedChat - The selected chat object
 * @param {Object} options - Migration options
 * @param {boolean} options.dryRun - If true, only simulate the migration without making changes
 * @param {boolean} options.deleteSource - If true, delete source messages after successful migration
 * @param {number} options.batchSize - Number of documents to process in each batch
 * @returns {Promise<MigrationResult>} - Migration result
 */
export const migrateMessagesForChat = withPerformanceMonitoring(
    'migrateMessagesForChat',
    async (selectedChat, options = {}) => {
        const {
            dryRun = false,
            deleteSource = false,
            batchSize = 50
        } = options;

        const result = {
            success: false,
            migratedCount: 0,
            skippedCount: 0,
            errorCount: 0,
            errors: [],
            fromPath: null,
            toPath: null
        };

        try {
            // Validate input
            if (!selectedChat) {
                throw new Error('selectedChat is required');
            }

            // Determine chat type and check if migration is needed
            const chatType = determineChatType(selectedChat);
            if (!chatType) {
                throw new Error('Unable to determine chat type');
            }

            if (!requiresMigration(chatType)) {
                result.success = true;
                result.skippedCount = 0;
                console.log(`Chat type ${chatType} does not require migration`);
                return result;
            }

            // Get dual write paths
            const dualWritePaths = getDualWritePaths(selectedChat, chatType);
            if (!dualWritePaths.legacy.messages || !dualWritePaths.current.messages) {
                throw new Error('Unable to determine source and destination paths');
            }

            result.fromPath = dualWritePaths.legacy.messages;
            result.toPath = dualWritePaths.current.messages;

            await logMigrationOperation(
                OPERATION_TYPES.MIGRATION,
                LOG_LEVELS.INFO,
                `Starting migration for ${chatType} chat`,
                {
                    chatId: dualWritePaths.chatId,
                    fromPath: result.fromPath,
                    toPath: result.toPath,
                    dryRun,
                    deleteSource,
                    phase: 'start'
                },
                dualWritePaths.chatId,
                chatType
            );

            console.log(`Starting migration for ${chatType} chat:`, {
                chatId: dualWritePaths.chatId,
                fromPath: result.fromPath,
                toPath: result.toPath,
                dryRun,
                deleteSource
            });

            // Check if source and destination are the same (no migration needed)
            if (result.fromPath === result.toPath) {
                result.success = true;
                result.skippedCount = 0;
                console.log('Source and destination paths are identical, no migration needed');
                return result;
            }

            // Get source messages
            const [sourceMainCollection, sourceChatId, sourceSubCollection] = result.fromPath.split('/');
            const sourceCollectionRef = collection(db, sourceMainCollection, sourceChatId, sourceSubCollection);
            const sourceQuery = query(sourceCollectionRef, orderBy('created_time', 'asc'));
            const sourceSnapshot = await getDocs(sourceQuery);

            if (sourceSnapshot.empty) {
                result.success = true;
                result.skippedCount = 0;
                console.log('No messages found in source collection');
                return result;
            }

            console.log(`Found ${sourceSnapshot.docs.length} messages to migrate`);

            // Check if destination already has messages (to avoid duplicates)
            const [destMainCollection, destChatId, destSubCollection] = result.toPath.split('/');
            const destCollectionRef = collection(db, destMainCollection, destChatId, destSubCollection);
            const destSnapshot = await getDocs(destCollectionRef);
            const existingMessageIds = new Set(destSnapshot.docs.map(doc => doc.id));

            console.log(`Destination collection has ${destSnapshot.docs.length} existing messages`);

            if (dryRun) {
                // Dry run: just count what would be migrated
                const messagesToMigrate = sourceSnapshot.docs.filter(doc => !existingMessageIds.has(doc.id));
                result.migratedCount = messagesToMigrate.length;
                result.skippedCount = sourceSnapshot.docs.length - messagesToMigrate.length;
                result.success = true;
                console.log(`Dry run complete: ${result.migratedCount} would be migrated, ${result.skippedCount} would be skipped`);
                return result;
            }

            // Process messages in batches
            const sourceMessages = sourceSnapshot.docs;
            const batches = [];

            for (let i = 0; i < sourceMessages.length; i += batchSize) {
                batches.push(sourceMessages.slice(i, i + batchSize));
            }

            console.log(`Processing ${batches.length} batches of up to ${batchSize} messages each`);

            // Ensure destination parent document exists
            await ensureParentDocumentExists(destMainCollection, destChatId, chatType);

            // Process each batch
            for (let batchIndex = 0; batchIndex < batches.length; batchIndex++) {
                const batch = batches[batchIndex];
                console.log(`Processing batch ${batchIndex + 1}/${batches.length} (${batch.length} messages)`);

                const batchResult = await migrateBatch(
                    batch,
                    destMainCollection,
                    destChatId,
                    destSubCollection,
                    existingMessageIds,
                    deleteSource ? { sourceMainCollection, sourceChatId, sourceSubCollection } : null
                );

                result.migratedCount += batchResult.migratedCount;
                result.skippedCount += batchResult.skippedCount;
                result.errorCount += batchResult.errorCount;
                result.errors.push(...batchResult.errors);
            }

            result.success = result.errorCount === 0;

            await logMigrationOperation(
                OPERATION_TYPES.MIGRATION,
                result.success ? LOG_LEVELS.SUCCESS : LOG_LEVELS.ERROR,
                `Migration ${result.success ? 'completed successfully' : 'completed with errors'}`,
                {
                    migratedCount: result.migratedCount,
                    skippedCount: result.skippedCount,
                    errorCount: result.errorCount,
                    phase: 'complete'
                },
                dualWritePaths.chatId,
                chatType
            );

            console.log(`Migration complete:`, {
                success: result.success,
                migratedCount: result.migratedCount,
                skippedCount: result.skippedCount,
                errorCount: result.errorCount
            });

            return result;

        } catch (error) {
            console.error('Migration failed:', error);
            result.errors.push({
                type: 'migration_error',
                message: error.message,
                error: error
            });
            return result;
        }
    },
    { chatId: selectedChat?.id, chatType: determineChatType(selectedChat) }
);

/**
 * Migrate a batch of messages
 * @private
 */
const migrateBatch = async (
    messageDocs,
    destMainCollection,
    destChatId,
    destSubCollection,
    existingMessageIds,
    deleteSourceInfo = null
) => {
    const batchResult = {
        migratedCount: 0,
        skippedCount: 0,
        errorCount: 0,
        errors: []
    };

    const batch = writeBatch(db);
    const deleteOperations = [];

    for (const messageDoc of messageDocs) {
        try {
            // Skip if message already exists in destination
            if (existingMessageIds.has(messageDoc.id)) {
                batchResult.skippedCount++;
                continue;
            }

            // Add message to destination
            const destDocRef = doc(db, destMainCollection, destChatId, destSubCollection, messageDoc.id);
            batch.set(destDocRef, messageDoc.data());

            // Prepare delete operation if requested
            if (deleteSourceInfo) {
                deleteOperations.push({
                    ref: doc(db, deleteSourceInfo.sourceMainCollection, deleteSourceInfo.sourceChatId, deleteSourceInfo.sourceSubCollection, messageDoc.id),
                    id: messageDoc.id
                });
            }

            batchResult.migratedCount++;

        } catch (error) {
            console.error(`Error preparing migration for message ${messageDoc.id}:`, error);
            batchResult.errorCount++;
            batchResult.errors.push({
                type: 'message_migration_error',
                messageId: messageDoc.id,
                message: error.message,
                error: error
            });
        }
    }

    // Execute the batch write
    if (batchResult.migratedCount > 0) {
        try {
            await batch.commit();
            console.log(`Successfully migrated batch of ${batchResult.migratedCount} messages`);

            // Delete source messages if requested and migration was successful
            if (deleteSourceInfo && deleteOperations.length > 0) {
                try {
                    const deleteBatch = writeBatch(db);
                    deleteOperations.forEach(({ ref }) => {
                        deleteBatch.delete(ref);
                    });
                    await deleteBatch.commit();
                    console.log(`Successfully deleted ${deleteOperations.length} source messages`);
                } catch (deleteError) {
                    console.error('Error deleting source messages:', deleteError);
                    batchResult.errors.push({
                        type: 'source_deletion_error',
                        message: deleteError.message,
                        error: deleteError,
                        affectedMessages: deleteOperations.map(op => op.id)
                    });
                }
            }

        } catch (error) {
            console.error('Error executing migration batch:', error);
            batchResult.errorCount += batchResult.migratedCount;
            batchResult.migratedCount = 0;
            batchResult.errors.push({
                type: 'batch_write_error',
                message: error.message,
                error: error
            });
        }
    }

    return batchResult;
};

/**
 * Ensure parent document exists for the destination collection
 * @private
 */
const ensureParentDocumentExists = async (mainCollection, chatId, chatType) => {
    try {
        const parentDocRef = doc(db, mainCollection, chatId);
        const parentDoc = await getDoc(parentDocRef);

        if (!parentDoc.exists()) {
            await setDoc(parentDocRef, {
                chatId,
                chatType,
                createdAt: new Date().toISOString(),
                lastActivity: new Date().toISOString(),
                migratedAt: new Date().toISOString()
            });
            console.log(`Created parent document: ${mainCollection}/${chatId}`);
        }
    } catch (error) {
        console.error('Error ensuring parent document exists:', error);
        // Don't throw - this is not critical for the migration
    }
};

/**
 * Verify data integrity by comparing source and destination collections
 * @param {Object} selectedChat - The selected chat object
 * @returns {Promise<Object>} - Verification result
 */
export const verifyMigrationIntegrity = async (selectedChat) => {
    const result = {
        success: false,
        sourceCount: 0,
        destinationCount: 0,
        missingInDestination: [],
        extraInDestination: [],
        dataIntegrityIssues: []
    };

    try {
        if (!selectedChat) {
            throw new Error('selectedChat is required');
        }

        const chatType = determineChatType(selectedChat);
        if (!requiresMigration(chatType)) {
            result.success = true;
            console.log(`Chat type ${chatType} does not require migration verification`);
            return result;
        }

        const dualWritePaths = getDualWritePaths(selectedChat, chatType);
        if (!dualWritePaths.legacy.messages || !dualWritePaths.current.messages) {
            throw new Error('Unable to determine source and destination paths');
        }

        // Get source messages
        const [sourceMainCollection, sourceChatId, sourceSubCollection] = dualWritePaths.legacy.messages.split('/');
        const sourceCollectionRef = collection(db, sourceMainCollection, sourceChatId, sourceSubCollection);
        const sourceSnapshot = await getDocs(sourceCollectionRef);
        result.sourceCount = sourceSnapshot.docs.length;

        // Get destination messages
        const [destMainCollection, destChatId, destSubCollection] = dualWritePaths.current.messages.split('/');
        const destCollectionRef = collection(db, destMainCollection, destChatId, destSubCollection);
        const destSnapshot = await getDocs(destCollectionRef);
        result.destinationCount = destSnapshot.docs.length;

        // Create maps for comparison
        const sourceMessages = new Map();
        sourceSnapshot.docs.forEach(doc => {
            sourceMessages.set(doc.id, doc.data());
        });

        const destMessages = new Map();
        destSnapshot.docs.forEach(doc => {
            destMessages.set(doc.id, doc.data());
        });

        // Find missing messages in destination
        for (const [messageId, messageData] of sourceMessages) {
            if (!destMessages.has(messageId)) {
                result.missingInDestination.push(messageId);
            } else {
                // Check data integrity
                const destData = destMessages.get(messageId);
                const integrityIssues = compareMessageData(messageData, destData, messageId);
                if (integrityIssues.length > 0) {
                    result.dataIntegrityIssues.push(...integrityIssues);
                }
            }
        }

        // Find extra messages in destination (shouldn't happen in normal migration)
        for (const messageId of destMessages.keys()) {
            if (!sourceMessages.has(messageId)) {
                result.extraInDestination.push(messageId);
            }
        }

        result.success = result.missingInDestination.length === 0 && result.dataIntegrityIssues.length === 0;

        console.log('Migration verification complete:', {
            success: result.success,
            sourceCount: result.sourceCount,
            destinationCount: result.destinationCount,
            missingCount: result.missingInDestination.length,
            extraCount: result.extraInDestination.length,
            integrityIssues: result.dataIntegrityIssues.length
        });

        return result;

    } catch (error) {
        console.error('Verification failed:', error);
        result.error = error.message;
        return result;
    }
};

/**
 * Compare message data for integrity verification
 * @private
 */
const compareMessageData = (sourceData, destData, messageId) => {
    const issues = [];
    const criticalFields = ['message', 'sender', 'recipient', 'created_time', 'type'];

    for (const field of criticalFields) {
        if (sourceData[field] !== destData[field]) {
            issues.push({
                messageId,
                field,
                sourceValue: sourceData[field],
                destValue: destData[field]
            });
        }
    }

    return issues;
};

/**
 * Rollback migration by moving messages from chat ID back to sender ID structure
 * @param {Object} selectedChat - The selected chat object
 * @param {Object} options - Rollback options
 * @param {boolean} options.dryRun - If true, only simulate the rollback
 * @param {boolean} options.deleteDestination - If true, delete destination messages after rollback
 * @returns {Promise<MigrationResult>} - Rollback result
 */
export const rollbackMigration = async (selectedChat, options = {}) => {
    const {
        dryRun = false,
        deleteDestination = false
    } = options;

    console.log('Starting migration rollback:', { dryRun, deleteDestination });

    try {
        // For rollback, we reverse the source and destination
        const chatType = determineChatType(selectedChat);
        if (!requiresMigration(chatType)) {
            return {
                success: true,
                migratedCount: 0,
                skippedCount: 0,
                errorCount: 0,
                errors: [],
                fromPath: null,
                toPath: null
            };
        }

        const dualWritePaths = getDualWritePaths(selectedChat, chatType);

        // Create a modified chat object for rollback (reverse the paths)
        const rollbackResult = await migrateMessagesForChat(selectedChat, {
            dryRun,
            deleteSource: deleteDestination,
            batchSize: 50
        });

        // Swap the paths in the result to reflect rollback direction
        const originalFromPath = rollbackResult.fromPath;
        rollbackResult.fromPath = rollbackResult.toPath;
        rollbackResult.toPath = originalFromPath;

        console.log('Migration rollback complete:', rollbackResult);
        return rollbackResult;

    } catch (error) {
        console.error('Rollback failed:', error);
        return {
            success: false,
            migratedCount: 0,
            skippedCount: 0,
            errorCount: 1,
            errors: [{
                type: 'rollback_error',
                message: error.message,
                error: error
            }],
            fromPath: null,
            toPath: null
        };
    }
};

/**
 * Get migration status for a chat
 * @param {Object} selectedChat - The selected chat object
 * @returns {Promise<Object>} - Migration status
 */
export const getMigrationStatus = async (selectedChat) => {
    try {
        const chatType = determineChatType(selectedChat);

        if (!requiresMigration(chatType)) {
            return {
                requiresMigration: false,
                chatType,
                status: 'not_required'
            };
        }

        const dualWritePaths = getDualWritePaths(selectedChat, chatType);

        // Check if source collection has messages
        const [sourceMainCollection, sourceChatId, sourceSubCollection] = dualWritePaths.legacy.messages.split('/');
        const sourceCollectionRef = collection(db, sourceMainCollection, sourceChatId, sourceSubCollection);
        const sourceSnapshot = await getDocs(sourceCollectionRef);
        const sourceCount = sourceSnapshot.docs.length;

        // Check if destination collection has messages
        const [destMainCollection, destChatId, destSubCollection] = dualWritePaths.current.messages.split('/');
        const destCollectionRef = collection(db, destMainCollection, destChatId, destSubCollection);
        const destSnapshot = await getDocs(destCollectionRef);
        const destCount = destSnapshot.docs.length;

        let status;
        if (sourceCount === 0 && destCount === 0) {
            status = 'no_data';
        } else if (sourceCount > 0 && destCount === 0) {
            status = 'not_migrated';
        } else if (sourceCount === 0 && destCount > 0) {
            status = 'fully_migrated';
        } else {
            status = 'partially_migrated';
        }

        return {
            requiresMigration: true,
            chatType,
            status,
            sourceCount,
            destCount,
            sourcePath: dualWritePaths.legacy.messages,
            destPath: dualWritePaths.current.messages
        };

    } catch (error) {
        console.error('Error getting migration status:', error);
        return {
            requiresMigration: false,
            error: error.message
        };
    }
};
