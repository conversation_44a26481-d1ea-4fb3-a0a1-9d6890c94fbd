import React, { useEffect, useState, useMemo } from "react";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";
import { Card, Form, Row, Col } from "react-bootstrap";
import { FaMagnifyingGlass } from "react-icons/fa6";
import DataTableComponent from "../../components/CustomDataTable/DataTable.component";
import FetchingDataLoading from "../../components/LoadingAnimation/FetchingDataLoading";
import PaginationRecordsForReports from "../../components/Reports/PaginationRecordsForReports";
import { useTranslatedColumns } from "../../components/Reports/ColumnsForTables.module";
import adsService from "../../services/ads";
import "./Ads.page.css";

export default function AdsPage() {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { adsColumns } = useTranslatedColumns();
  const [loading, setLoading] = useState(false);
  const [ads, setAds] = useState([]);
  const [adsApiResponse, setAdsApiResponse] = useState(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [recordsPerPage, setRecordsPerPage] = useState(10);
  const [searchTerm, setSearchTerm] = useState("");

  // Effect to fetch ads when pagination changes
  useEffect(() => {
    fetchAds();
  }, [currentPage, recordsPerPage]);

  const fetchAds = async () => {
    setLoading(true);
    try {
      const response = await adsService.getAllAdsApi(
        recordsPerPage,
        currentPage
      );

      if (
        (response?.success || response?.message === "Success") &&
        response?.data
      ) {
        // Handle nested data structure - ads are in response.data.data
        setAds(response.data.data || []);
        setAdsApiResponse(response.data); // Store full API response for pagination
      } else {
        setAds([]);
        setAdsApiResponse(null);
      }
    } catch (error) {
      console.error("Error fetching ads:", error);
      setAds([]);
      setAdsApiResponse(null);
    } finally {
      setLoading(false);
    }
  };

  const handleSearchChange = (e) => {
    const newSearchTerm = e.target.value;
    setSearchTerm(newSearchTerm);
    // Reset to first page when searching (frontend filtering)
    setCurrentPage(1);
  };

  const handleViewLeads = (ad) => {
    navigate(`/ads/${ad.ad_id}/leads`, {
      state: {
        adInfo: {
          ad_id: ad.ad_id,
          ad_name: ad.ad_name,
          campaign_name: ad.campaign_name,
          page_name: ad.page_name,
        },
      },
    });
  };

  // Pagination handlers for ads
  const handleAdsPageChange = (url) => {
    if (!url) return;

    // Extract page and per_page from URL
    const urlParams = new URLSearchParams(url.split("?")[1]);
    const page = parseInt(
      urlParams.get("current_page") || urlParams.get("page") || "1"
    );
    const perPage = parseInt(urlParams.get("per_page") || recordsPerPage);

    setCurrentPage(page);
    setRecordsPerPage(perPage);
  };

  const handleAdsPageSizeChange = (size) => {
    setRecordsPerPage(size);
    setCurrentPage(1); // Reset to first page when changing page size
  };

  // Get ads columns from the module
  const adsColumnsConfig = useMemo(
    () => adsColumns(handleViewLeads),
    [adsColumns, handleViewLeads]
  );

  // Filter ads based on search term (frontend filtering)
  const filteredAds = useMemo(() => {
    // Ensure ads is always an array
    const adsArray = Array.isArray(ads) ? ads : [];

    if (!searchTerm.trim()) {
      return adsArray;
    }

    const searchLower = searchTerm.toLowerCase();
    return adsArray.filter((ad) => {
      return (
        ad.ad_name?.toLowerCase().includes(searchLower) ||
        ad.campaign_name?.toLowerCase().includes(searchLower) ||
        ad.page_name?.toLowerCase().includes(searchLower) ||
        ad.ad_id?.toString().includes(searchLower)
      );
    });
  }, [ads, searchTerm]);

  // Since we're doing frontend filtering, we need to handle pagination for filtered data
  const data = useMemo(() => {
    return filteredAds;
  }, [filteredAds]);

  return (
    <>
      <h3 className="my-4">{t("adsTable.title")}</h3>
      {loading ? (
        <FetchingDataLoading />
      ) : (
        <>
          <div
            className="mb-3 content-container"
            style={{
              opacity: loading ? 0.5 : 1,
            }}
          >
            <Row className="justify-content-end align-items-center">
              <Col lg={4} md={4} sm={12} className="mb-3">
                <Form.Group className="position-relative">
                  <Form.Control
                    placeholder={`${t(
                      "tableControls.placeholders.searchTable"
                    )} ${filteredAds.length} ${t(
                      "tableControls.placeholders.records"
                    )}...`}
                    value={searchTerm}
                    onChange={handleSearchChange}
                    className="rounded-pill"
                  />
                  <FaMagnifyingGlass
                    className="text-muted position-absolute"
                    style={{
                      right: "10px",
                      top: "50%",
                      transform: "translateY(-50%)",
                    }}
                  />
                </Form.Group>
              </Col>
            </Row>

            <DataTableComponent
              columns={adsColumnsConfig}
              data={data || []}
              loading={loading}
              initialSortBy={[]}
              hiddenColumns={[]}
              noDataFound={t("adsTable.noAdsFound") || "No Ads Found"}
            />

            <PaginationRecordsForReports
              onPageChange={handleAdsPageChange}
              links={adsApiResponse?.links || []}
              handlePageSizeChange={handleAdsPageSizeChange}
              per_page={adsApiResponse?.per_page || recordsPerPage}
              to={adsApiResponse?.to || 0}
              total={adsApiResponse?.total || 0}
              currentPage={adsApiResponse?.current_page || currentPage}
            />
          </div>
        </>
      )}
    </>
  );
}
