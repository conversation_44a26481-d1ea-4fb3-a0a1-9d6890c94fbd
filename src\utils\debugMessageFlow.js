/**
 * Debug utilities for Firebase Collection Consistency Message Flow
 *
 * This utility helps diagnose issues with message sending and receiving
 * in the new Firebase collection consistency implementation.
 */

import { determineChatType, getChatIdentifier, getCollectionPaths } from '../services/firebase/collectionPaths';

/**
 * Debug message flow for a specific chat
 * @param {Object} selectedChat - The selected chat object
 * @param {Object} selectedPage - The selected page object
 * @returns {Object} Debug information
 */
export const debugChatMessageFlow = (selectedChat, selectedPage) => {
    if (!selectedChat) {
        return { error: 'No selected chat provided' };
    }

    try {
        // Determine chat type and paths
        const chatType = determineChatType(selectedChat);
        const chatId = getChatIdentifier(selectedChat, chatType);
        const collectionPaths = getCollectionPaths(selectedChat, chatType, selectedPage?.page_id);

        const debugInfo = {
            timestamp: new Date().toISOString(),
            selectedChat: {
                id: selectedChat.id,
                type: chatType,
                participants: selectedChat.participants?.data?.map(p => ({ id: p.id })),
                sender_phone_number: selectedChat.sender_phone_number,
                flage: selectedChat.flage
            },
            selectedPage: {
                id: selectedPage?.page_id,
                page_token: selectedPage?.page_token ? '[PRESENT]' : '[MISSING]'
            },
            collectionPaths: {
                chatId,
                chatType,
                messages: collectionPaths.messages,
                comments: collectionPaths.comments
            },
            expectedFirebasePaths: {
                messagesCollection: collectionPaths.messages,
                commentsCollection: collectionPaths.comments
            },
            checks: {
                hasChatId: !!chatId,
                hasChatType: !!chatType,
                hasValidPaths: !!(collectionPaths.messages && collectionPaths.comments),
                hasPageId: !!selectedPage?.page_id
            }
        };

        // Add warnings for potential issues
        debugInfo.warnings = [];

        if (!chatId) {
            debugInfo.warnings.push('Chat ID could not be determined - check chat object structure');
        }

        if (!chatType) {
            debugInfo.warnings.push('Chat type could not be determined - check chat object structure');
        }

        if (!selectedPage?.page_id) {
            debugInfo.warnings.push('Page ID missing - latest messages listener may not work');
        }

        if (!collectionPaths.messages) {
            debugInfo.warnings.push('Message path could not be determined');
        }

        return debugInfo;

    } catch (error) {
        return {
            error: 'Error debugging chat message flow',
            details: error.message,
            stack: error.stack
        };
    }
};

/**
 * Debug Firebase listener setup
 * @param {Object} debugInfo - Debug info from debugChatMessageFlow
 * @returns {Object} Listener debug information
 */
export const debugFirebaseListeners = (debugInfo) => {
    if (debugInfo.error) {
        return debugInfo;
    }

    const listenerInfo = {
        timestamp: new Date().toISOString(),
        expectedListeners: {
            latestMessages: {
                collection: 'chats',
                filter: `page_id == ${debugInfo.selectedPage.id}`,
                purpose: 'Updates chat list with latest messages'
            },
            chatMessages: {
                collection: debugInfo.expectedFirebasePaths.messagesCollection,
                purpose: 'Real-time updates for current chat messages'
            },
            legacyMessages: debugInfo.collectionPaths.requiresMigration ? {
                collection: debugInfo.expectedFirebasePaths.legacyMessagesCollection,
                purpose: 'Fallback for legacy message data'
            } : null
        },
        troubleshooting: {
            latestMessagesNotUpdating: [
                'Check if page_id is being written to chat documents',
                'Verify latest messages listener is active',
                'Check console for Firebase listener errors'
            ],
            messagesNotAppearing: [
                'Check if messages are being written to correct collection path',
                'Verify chat messages listener is set up correctly',
                'Check if updateMessages reducer is being called'
            ],
            incomingMessagesNotWorking: [
                'Check if webhook is writing to correct Firebase paths',
                'Verify incoming messages include page_id field',
                'Check if Firebase security rules allow writes'
            ]
        }
    };

    return listenerInfo;
};

/**
 * Test message flow by simulating a message send
 * @param {Object} selectedChat - The selected chat object
 * @param {Object} selectedPage - The selected page object
 * @param {string} testMessage - Test message content
 * @returns {Object} Test results
 */
export const testMessageFlow = async (selectedChat, selectedPage, testMessage = 'Test message') => {
    const debugInfo = debugChatMessageFlow(selectedChat, selectedPage);

    if (debugInfo.error) {
        return debugInfo;
    }

    const testResults = {
        timestamp: new Date().toISOString(),
        debugInfo,
        testMessage,
        steps: []
    };

    try {
        // Step 1: Validate inputs
        testResults.steps.push({
            step: 1,
            name: 'Validate inputs',
            status: debugInfo.warnings.length === 0 ? 'PASS' : 'WARN',
            details: debugInfo.warnings.length === 0 ? 'All inputs valid' : debugInfo.warnings
        });

        // Step 2: Check collection paths
        testResults.steps.push({
            step: 2,
            name: 'Check collection paths',
            status: debugInfo.checks.hasValidPaths ? 'PASS' : 'FAIL',
            details: debugInfo.expectedFirebasePaths
        });

        // Step 3: Simulate message data structure
        const messageData = {
            id: `test_${Date.now()}`,
            message: testMessage,
            sender: debugInfo.selectedChat.participants?.[0]?.id || 'test_sender',
            recipient: debugInfo.selectedChat.participants?.[1]?.id || 'test_recipient',
            created_time: new Date().toISOString(),
            updated_time: new Date().toISOString(),
            type: 'text',
            page_id: selectedPage?.page_id,
            chat_id: debugInfo.collectionPaths.chatId,
            chat_type: debugInfo.collectionPaths.chatType
        };

        testResults.steps.push({
            step: 3,
            name: 'Generate message data structure',
            status: 'PASS',
            details: messageData
        });

        // Step 4: Check expected Firebase operations
        const expectedOperations = [];

        if (debugInfo.expectedFirebasePaths.latestMessageDocument) {
            expectedOperations.push({
                operation: 'setDoc',
                path: debugInfo.expectedFirebasePaths.latestMessageDocument,
                data: messageData,
                purpose: 'Update latest message for chat list'
            });
        }

        if (debugInfo.expectedFirebasePaths.messagesCollection) {
            expectedOperations.push({
                operation: 'setDoc',
                path: `${debugInfo.expectedFirebasePaths.messagesCollection}/${messageData.id}`,
                data: messageData,
                purpose: 'Store message in messages collection'
            });
        }

        if (debugInfo.collectionPaths.requiresMigration && debugInfo.expectedFirebasePaths.legacyMessagesCollection) {
            expectedOperations.push({
                operation: 'setDoc',
                path: `${debugInfo.expectedFirebasePaths.legacyMessagesCollection}/${messageData.id}`,
                data: messageData,
                purpose: 'Dual write to legacy collection for backward compatibility'
            });
        }

        testResults.steps.push({
            step: 4,
            name: 'Expected Firebase operations',
            status: expectedOperations.length > 0 ? 'PASS' : 'FAIL',
            details: expectedOperations
        });

        // Overall test result
        const allStepsPass = testResults.steps.every(step => step.status === 'PASS');
        testResults.overallStatus = allStepsPass ? 'PASS' : 'FAIL';

        return testResults;

    } catch (error) {
        testResults.error = error.message;
        testResults.overallStatus = 'ERROR';
        return testResults;
    }
};

/**
 * Console helper to debug message flow
 * Usage: window.debugMessageFlow(selectedChat, selectedPage)
 */
if (typeof window !== 'undefined') {
    window.debugMessageFlow = (selectedChat, selectedPage) => {
        const debugInfo = debugChatMessageFlow(selectedChat, selectedPage);
        const listenerInfo = debugFirebaseListeners(debugInfo);

        console.group('🔍 Message Flow Debug');
        console.log('Chat Debug Info:', debugInfo);
        console.log('Listener Debug Info:', listenerInfo);
        console.groupEnd();

        return { debugInfo, listenerInfo };
    };

    window.testMessageFlow = testMessageFlow;
}

export default {
    debugChatMessageFlow,
    debugFirebaseListeners,
    testMessageFlow
};
