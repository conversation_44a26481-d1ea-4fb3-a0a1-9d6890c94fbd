import { Tooltip } from "react-tooltip";
import { format, parseISO } from "date-fns";
import { ReactSVG } from "react-svg";
import { IoSparklesSharp } from "react-icons/io5";
import { Link } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { useMemo } from "react";
import { Badge, Button, Form, Spinner, Dropdown } from "react-bootstrap";
import { FaUserPlus, FaPhone, FaEnvelope, FaCalendar } from "react-icons/fa6";
import { MdAssignmentTurnedIn, MdEdit } from "react-icons/md";
import { PiTrashFill } from "react-icons/pi";
import { FaEye, FaUserCog } from "react-icons/fa";
import { BsEye } from "react-icons/bs";
import StatusDropdown from './StatusDropdown';
import PriorityDropdown from './PriorityDropdown';
import { sourceToIcon } from "../../constants/sourceIcons";
import { getAllLeadStatuses } from "../../config/leadStatusConfig";
import { isUserExcluded } from "../../config/packageVisibility";

// Define constants for lead statuses to avoid hardcoding
const LEAD_STATUS = {
  PENDING: 0,
  IN_PROGRESS: 1,
  COMPLETED: 2,
  REJECTED: 3,
  WRONG_LEAD: 4,
  NOT_QUALIFIED: 5,
  NO_COMMUNICATION: 6,
  BOOKED: 7,
  BOOKED_AND_RESERVED: 8,
  CANCELED: 9,
  QUOTATION: 10,
  ASSIGNED: 11
};

export const createTranslatedColumns = (t, user = null) => {
  const leadAssignmentColumns = [
    {
      Header: "#",
      accessor: "leadId",
      Cell: ({ row }) => <Link to={`/leads/${row.original.id}`}>{row.index + 1}</Link>,
    },
    {
      Header: t('tables.headers.name'),
      accessor: "name",
      Cell: ({ row }) => {
        const name = row.original.name;
        return (
          <>
            <Link
              className={`one-line lead-name-reports${row.original.id}`}
              style={{ maxWidth: "100px" }}
              to={`/leads/${row.original.id}`}>
              {row.original.status === LEAD_STATUS.PENDING ? <IoSparklesSharp className={"mainColor"} /> : null}  {name}
            </Link>
            <Tooltip
              anchorSelect={`.lead-name-reports${row.original.id}`}
              content={name}
              className={"bg-dark text-white"}
            />
          </>
        );
      },
    },
    {
      Header: t('tables.headers.phone'),
      accessor: "phone",
      Cell: ({ row }) => {
        const phone = row.original.phone;
        return (
          <>
            <Link
              to={`/leads/${row.original.id}`}
              className={`one-line lead-phone-reports${row.original.id}`}
              style={{ maxWidth: "100px" }}
            >
              {phone}
            </Link>
            <Tooltip
              anchorSelect={`.lead-phone-reports${row.original.id}`}
              content={phone}
              className={"bg-dark text-white"}
            />
          </>
        );
      }
    },
    {
      Header: t('tables.headers.assignedTo'),
      accessor: "assigned_name",
      Cell: ({ row }) => {
        const assignedName = row.original.assigned_name;
        return (
          <>
            <Link
              to={`/leads/${row.original.id}`}
              className={`one-line assigned-name-reports${row.original.id}`}
              style={{ maxWidth: "150px" }}
            >
              {assignedName}
            </Link>
            <Tooltip
              anchorSelect={`.assigned-name-reports${row.original.id}`}
              content={assignedName}
              className={"bg-dark text-white"}
            />
          </>
        );
      }
    },
    { Header: t('tables.headers.service'), accessor: "service" },
    {
      Header: t('tables.headers.source'),
      accessor: "source",
      Cell: ({ row }) => {
        const source = row.original.source;
        const IconComponent = sourceToIcon[source] || null;

        return (
          <Link to={`/leads/${row.original.id}`} className={"mx-auto social-icon-container"}>
            {IconComponent && <ReactSVG src={IconComponent} />}
          </Link>
        );
      },
    },
    {
      Header: t('tables.headers.createdAt'),
      accessor: "created_at",
      Cell: ({ value, row }) => {
        if (value) {
          const parsedDate = new Date(value);
          if (!isNaN(parsedDate.getTime())) {
            const formattedDate = format(parsedDate, "yyyy-MM-dd HH:mm");
            return (
              <>
                <Link
                  to={`/leads/${row.original.id}`}
                  className={`one-line created-at-reports${row.original.id}`}
                  style={{ maxWidth: "160px" }}
                >
                  {formattedDate}
                </Link>
                <Tooltip
                  anchorSelect={`.created-at-reports${row.original.id}`}
                  content={formattedDate}
                  className={"bg-dark text-white"}
                />
              </>
            );
          }
        }
        return (
          <>
            <Link
              to={`/leads/${row.original.id}`}
              className={`one-line created-at-reports${row.original.id}`}
              style={{ maxWidth: "160px" }}
            >
              {value}
            </Link>
            <Tooltip
              anchorSelect={`.created-at-reports${row.original.id}`}
              content={value}
              className={"bg-dark text-white"}
            />
          </>
        );
      },
    },
    {
      Header: t('tables.headers.status'),
      accessor: "status",
      Cell: ({ row }) => {
        const status = Number(row?.original?.status);
        const userId = user?.user?.id || user?.id;
        const isSpecialUser = isUserExcluded(userId);
        const allStatuses = getAllLeadStatuses();

        // Get the status text based on user type
        const getStatusText = (statusCode) => {
          // For special users, only show specific statuses
          if (isSpecialUser) {
            // Always visible statuses (Pending and Assigned)
            if (statusCode === LEAD_STATUS.PENDING) return t('tables.status.pending');
            if (statusCode === LEAD_STATUS.ASSIGNED) return t('tables.status.assigned');

            // Extended statuses (12-26)
            if (statusCode >= 12 && statusCode <= 26) {
              switch (statusCode) {
                case 12: return t('tables.status.undefined');
                case 13: return t('tables.status.advancepaid');
                case 14: return t('tables.status.followup');
                case 15: return t('tables.status.notinterested');
                case 16: return t('tables.status.junk');
                case 17: return t('tables.status.complaints');
                case 18: return t('tables.status.urgentcall');
                case 19: return t('tables.status.callback');
                case 20: return t('tables.status.bookedaction2');
                case 21: return t('tables.status.sentsms');
                case 22: return t('tables.status.noaction');
                case 23: return t('tables.status.notinterestedaction2');
                case 24: return t('tables.status.whatapp-nal');
                case 25: return t('tables.status.followupaction2');
                case 26: return t('tables.status.complainaction2');
                default: return allStatuses[statusCode] || t('tables.status.unknown');
              }
            }

            // For special users, don't show other basic statuses
            return t('status.unknown');
          }

          // For regular users, show all basic statuses
          switch (statusCode) {
            case LEAD_STATUS.PENDING: return t('tables.status.pending');
            case LEAD_STATUS.IN_PROGRESS: return t('tables.status.inProgress');
            case LEAD_STATUS.ASSIGNED: return t('tables.status.assigned');
            case LEAD_STATUS.COMPLETED: return t('tables.status.completed');
            case LEAD_STATUS.REJECTED: return t('tables.status.rejected');
            case LEAD_STATUS.WRONG_LEAD: return t('tables.status.wrongLead');
            case LEAD_STATUS.NOT_QUALIFIED: return t('tables.status.notQualified');
            case LEAD_STATUS.NO_COMMUNICATION: return t('tables.status.noCommunication');
            case LEAD_STATUS.BOOKED: return t('tables.status.booked');
            case LEAD_STATUS.BOOKED_AND_RESERVED: return t('tables.status.bookedReserved');
            case LEAD_STATUS.CANCELED: return t('tables.status.canceled');
            case LEAD_STATUS.QUOTATION: return t('tables.status.quotation');
            default: return allStatuses[statusCode] || t('tables.status.unknown');
          }
        };

        const statusText = getStatusText(status);

        return (
          <>
            <Link
              to={`/leads/${row.original.id}`}
              className={`one-line status-reports${row.original.id} ${status === LEAD_STATUS.PENDING ? "pending-status" : ""} ${status === LEAD_STATUS.IN_PROGRESS ? "in-progress-status" : ""} ${status === LEAD_STATUS.COMPLETED ? "completed-status" : ""} ${status === LEAD_STATUS.REJECTED ? "rejected-status" : ""} ${status === LEAD_STATUS.WRONG_LEAD ? "wrong-lead-status" : ""} ${status === LEAD_STATUS.NOT_QUALIFIED ? "not-qualified-status" : ""} ${status === LEAD_STATUS.NO_COMMUNICATION ? "no-communication-status" : ""} ${status === LEAD_STATUS.BOOKED ? "booked-status" : ""} ${status === LEAD_STATUS.BOOKED_AND_RESERVED ? "booked-reserved-status" : ""} ${status === LEAD_STATUS.CANCELED ? "canceled-status" : ""} rounded-pill p-1 fw-bold`}
              style={{ fontSize: "0.8rem", maxWidth: "150px" }}
            >
              {statusText}
            </Link>
            <Tooltip
              anchorSelect={`.status-reports${row.original.id}`}
              content={statusText}
              className={"bg-dark text-white"}
            />
          </>
        );
      },
    },
  ];

  const teamMembersColumns = [
    {
      Header: "#",
      accessor: "teamMemberId",
      Cell: ({ row }) => <span>{row.index + 1}</span>,
    },
    {
      Header: t('tables.headers.member'),
      accessor: "name",
      Cell: ({ row }) => {
        const name = row.original.name;
        return (
          <>
            <div
              className={`one-line tm-name-${row.original.id}`}
              style={{ maxWidth: "150px" }}
            >
              {name}
            </div>
            <Tooltip
              anchorSelect={`.tm-name-${row.original.id}`}
              content={name}
              className={"bg-dark text-white"}
            />
          </>
        );
      },
    },
    {
      Header: t('tables.headers.role'),
      accessor: "role",
      Cell: ({ row }) => {
        const Role = row.original.role;
        const roleKey = {
          0: 'roles.admin',
          1: 'roles.admin',
          2: 'roles.moderator',
          3: 'roles.sales',
          4: 'roles.accountant',
          5: 'roles.teamMember'
        }[Role] || 'roles.moderator';

        return (
          <div className={"d-flex justify-content-center align-items-center"}>
            <div className={"shadow-sm rounded-2 p-1"}>{t(roleKey)}</div>
          </div>
        );
      },
    },
    { Header: t('tables.headers.status'), accessor: "status" },
    { Header: t('tables.headers.totalLeads'), accessor: "total_leads" },
    { Header: t('tables.headers.completed'), accessor: "completed_leads" },
    { Header: t('tables.headers.inProgress'), accessor: "in_progress_leads" },
    { Header: t('tables.headers.rejected'), accessor: "rejected_leads" },
    { Header: t('tables.headers.booked'), accessor: "booked_leads" },
    { Header: t('tables.headers.assigned'), accessor: "assigned_leads" },
  ];

  const salesColumns = [
    {
      Header: "#",
      accessor: "salesId",
      Cell: ({ row }) => <Link to={`/leads/${row.original.lead_id}`}>{row.index + 1}</Link>,
    },
    {
      Header: t('tables.headers.member'),
      accessor: "team_name",
      Cell: ({ row }) => {
        const leadId = row.original.lead_id;
        const value = row.original.team_name;
        return (
          <>
            <Link
              to={`/leads/${leadId}`}
              className={`one-line sales-tm-${row.index}`}
              style={{ maxWidth: '140px' }}
            >
              {value}
            </Link>
            <Tooltip
              anchorSelect={`.sales-tm-${row.index}`}
              content={value}
              className={"bg-dark text-white"}
            />
          </>
        );
      }
    },
    {
      Header: t('tables.headers.lead'),
      accessor: "lead_name",
      Cell: ({ row }) => {
        const leadId = row.original.lead_id;
        const value = row.original.lead_name;
        return (
          <>
            <Link
              to={`/leads/${leadId}`}
              className={`one-line sales-lead-${row.original.lead_id}`}
              style={{ maxWidth: '140px' }}
            >
              {value}
            </Link>
            <Tooltip
              anchorSelect={`.sales-lead-${row.original.lead_id}`}
              content={value}
              className={"bg-dark text-white"}
            />
          </>
        );
      }
    },
    {
      Header: t('tables.headers.inProgress'),
      accessor: "in_progress",
      Cell: ({ row }) => {
        const status = Number(row?.original?.lead_status);
        return (
          <>
            <Link
              to={`/leads/${row.original.lead_id}`}
              className={`one-line mx-auto sales-inprogress-${row.index}`}
              style={{ maxWidth: "200px" }}
            >
              {status === LEAD_STATUS.IN_PROGRESS ? row?.original?.Act_note : "-"}
            </Link>
            {status === LEAD_STATUS.IN_PROGRESS && (
              <Tooltip
                anchorSelect={`.sales-inprogress-${row.index}`}
                content={row?.original?.Act_note}
                className={"bg-dark text-white"}
              />
            )}
          </>
        );
      },
    },
    {
      Header: t('tables.headers.createdAt'),
      accessor: "create",
      Cell: ({ row }) => {
        const dateStr = row?.original?.Act_created_at.slice(0, -3);
        return (
          <>
            <Link
              to={`/leads/${row.original.lead_id}`}
              className={`one-line sales-created-${row.index}`}
              style={{ maxWidth: "160px" }}
            >
              {dateStr}
            </Link>
            <Tooltip
              anchorSelect={`.sales-created-${row.index}`}
              content={dateStr}
              className={"bg-dark text-white"}
            />
          </>
        );
      }
    },
    {
      Header: t('tables.headers.completed'),
      accessor: "completed",
      Cell: ({ row }) => {
        const status = Number(row?.original?.lead_status);
        const dateWithoutSeconds = row?.original?.Act_created_at.slice(0, -3);
        return (
          <>
            <Link
              to={`/leads/${row.original.lead_id}`}
              className={`one-line sales-completed-${row.index}`}
              style={{ maxWidth: "160px" }}
            >
              {status === LEAD_STATUS.COMPLETED ? dateWithoutSeconds : "-"}
            </Link>
            {status === LEAD_STATUS.COMPLETED && (
              <Tooltip
                anchorSelect={`.sales-completed-${row.index}`}
                content={dateWithoutSeconds}
                className={"bg-dark text-white"}
              />
            )}
          </>
        );
      },
    },
    {
      Header: t('tables.headers.rejected'),
      accessor: "rejected",
      Cell: ({ row }) => {
        const status = Number(row?.original?.lead_status);
        const dateWithoutSeconds = row?.original?.Act_created_at.slice(0, -3);
        return (
          <>
            <Link
              to={`/leads/${row.original.lead_id}`}
              className={`one-line sales-rejected-${row.index}`}
              style={{ maxWidth: "160px" }}
            >
              {status === LEAD_STATUS.REJECTED ? dateWithoutSeconds : "-"}
            </Link>
            {status === LEAD_STATUS.REJECTED && (
              <Tooltip
                anchorSelect={`.sales-rejected-${row.index}`}
                content={dateWithoutSeconds}
                className={"bg-dark text-white"}
              />
            )}
          </>
        );
      },
    },
    {
      Header: t('tables.headers.service'),
      accessor: "service",
      Cell: ({ row }) => {
        const service = row.original.service;
        return (
          <>
            <Link
              to={`/leads/${row.original.lead_id}`}
              className={`one-line sales-service-${row.index}`}
              style={{ maxWidth: "120px" }}
            >
              {service || "-"}
            </Link>
            <Tooltip
              anchorSelect={`.sales-service-${row.index}`}
              content={service || "-"}
              className={"bg-dark text-white"}
            />
          </>
        );
      },
    },
  ];

  const statisticsForTMColumns = [
    {
      Header: "#",
      accessor: "teamMemberId",
      Cell: ({ row }) => <span>{row.index + 1}</span>,
    },
    {
      Header: t('tables.headers.lead'),
      accessor: "name",
      Cell: ({ row }) => {
        const value = row.original.name;
        return (
          <>
            <div className={`one-line statistics-lead-${row.index}`} style={{ maxWidth: "150px" }}>{value}</div>
            <Tooltip anchorSelect={`.statistics-lead-${row.index}`} content={value} className={"bg-dark text-white"} />
          </>
        );
      },
    },
    {
      Header: t('tables.headers.phone'),
      accessor: "phone",
      Cell: ({ row }) => {
        const value = row.original.phone;
        return (
          <>
            <div className={`one-line statistics-phone-${row.index}`} style={{ maxWidth: "120px" }}>{value}</div>
            <Tooltip anchorSelect={`.statistics-phone-${row.index}`} content={value} className={"bg-dark text-white"} />
          </>
        );
      },
    },
    {
      Header: t('tables.headers.pageName'),
      accessor: "page_name",
      Cell: ({ row }) => {
        const value = row.original.page_name;
        return (
          <>
            <div className={`one-line statistics-page-${row.index}`} style={{ maxWidth: "150px" }}>{value}</div>
            <Tooltip anchorSelect={`.statistics-page-${row.index}`} content={value} className={"bg-dark text-white"} />
          </>
        );
      },
    },
    {
      Header: t('tables.headers.formName'),
      accessor: "form_name",
      Cell: ({ row }) => {
        const value = row.original.form_name;
        return (
          <>
            <div className={`one-line statistics-form-${row.index}`} style={{ maxWidth: "150px" }}>{value}</div>
            <Tooltip anchorSelect={`.statistics-form-${row.index}`} content={value} className={"bg-dark text-white"} />
          </>
        );
      },
    },
    {
      Header: t('tables.headers.source'),
      accessor: "source",
      Cell: ({ row }) => {
        const source = row.original.source;
        const IconComponent = sourceToIcon[source] || null;

        return (
          <div className={"mx-auto social-icon-container"}>
            {IconComponent && <ReactSVG src={IconComponent} />}
          </div>
        );
      },
    },
    {
      Header: t('tables.headers.status'),
      accessor: "status",
      Cell: ({ row }) => {
        const status = row?.original?.status;
        return (
          <div
            className={`${status === LEAD_STATUS.PENDING && "pending-status"} ${status === LEAD_STATUS.IN_PROGRESS ? "in-progress-status" : null} ${status === LEAD_STATUS.COMPLETED && "completed-status"} ${status === LEAD_STATUS.REJECTED && "rejected-status"} ${status === LEAD_STATUS.WRONG_LEAD && "wrong-lead-status"} ${status === LEAD_STATUS.NOT_QUALIFIED && "not-qualified-status"} ${status === LEAD_STATUS.NO_COMMUNICATION && "no-communication-status"} ${status === LEAD_STATUS.BOOKED && "booked-status"} ${status === LEAD_STATUS.BOOKED_AND_RESERVED && "booked-reserved-status"} ${status === LEAD_STATUS.CANCELED && "canceled-status"} rounded-pill p-1 fw-bold`}
            style={{ fontSize: "0.8rem" }}
          >
            {status === LEAD_STATUS.PENDING && t('status.pending')}
            {status === LEAD_STATUS.ASSIGNED && t('status.assigned')}
            {status === LEAD_STATUS.IN_PROGRESS && t('status.inProgress')}
            {status === LEAD_STATUS.COMPLETED && t('status.completed')}
            {status === LEAD_STATUS.REJECTED && t('status.rejected')}
            {status === LEAD_STATUS.WRONG_LEAD && t('status.wrongLead')}
            {status === LEAD_STATUS.NOT_QUALIFIED && t('status.notQualified')}
            {status === LEAD_STATUS.NO_COMMUNICATION && t('status.noCommunication')}
            {status === LEAD_STATUS.BOOKED && t('status.booked')}
            {status === LEAD_STATUS.BOOKED_AND_RESERVED && t('status.bookedReserved')}
            {status === LEAD_STATUS.CANCELED && t('status.canceled')}
          </div>
        );
      },
    },
    { Header: t('tables.headers.amount'), accessor: "amount" },
  ];

  const newLeadsForAdmin = [
    {
      Header: t('tables.headers.id'),
      accessor: "leadId",
      Cell: ({ row }) => <span>{row.index + 1}</span>,
    },
    {
      Header: t('tables.headers.leadName'),
      accessor: "name",
      Cell: ({ row }) => {
        const leadName = row.original.name;
        return (
          <>
            <div className={`text-nowrap overflow-hidden text-center mx-auto one-line lead-name-${row.original.id}`} style={{ maxWidth: "200px" }}>
              {leadName}
            </div>
            <Tooltip
              anchorSelect={`.lead-name-${row.original.id}`}
              content={leadName}
              className={"bg-white text-dark"}
            />
          </>
        )
      },
    },
    {
      Header: t('tables.headers.email'),
      accessor: "email",
      Cell: ({ row }) => {
        const email = row.original.email;
        return (
          <>
            <div className={`text-nowrap overflow-hidden text-center mx-auto one-line lead-email-${row.original.id}`} style={{ maxWidth: "200px" }}>
              {email}
            </div>
            <Tooltip
              anchorSelect={`.lead-email-${row.original.id}`}
              content={email}
              className={"bg-white text-dark"}
            />
          </>
        )
      },
    },
    {
      Header: t('tables.headers.phone'),
      accessor: "phone",
      Cell: ({ row }) => {
        const phone = row.original.phone;
        return (
          <>
            <div
              className={`one-line admin-phone-${row.original.id}`}
              style={{ maxWidth: "150px" }}
            >
              {phone}
            </div>
            <Tooltip
              anchorSelect={`.admin-phone-${row.original.id}`}
              content={phone}
              className={"bg-dark text-white"}
            />
          </>
        )
      },
    },
    {
      Header: t('tables.headers.source'),
      accessor: "source",
      Cell: ({ row }) => {
        const source = row.original.source;
        const IconComponent = sourceToIcon[source] || null;

        return (
          <div className={"mx-auto social-icon-container"}>
            {IconComponent && <ReactSVG src={IconComponent} />}
          </div>
        );
      },
    },
    {
      Header: t('tables.headers.createdBy'),
      accessor: "create.name",
      Cell: ({ row }) => {
        const creatorName = row.original.create?.name || '';
        return (
          <>
            <div
              className={`one-line admin-createdby-${row.original.id}`}
              style={{ maxWidth: "160px" }}
            >
              {creatorName}
            </div>
            <Tooltip
              anchorSelect={`.admin-createdby-${row.original.id}`}
              content={creatorName}
              className={"bg-dark text-white"}
            />
          </>
        );
      },
    },
    {
      Header: t('tables.headers.status'),
      accessor: "status",
      Cell: ({ value, row }) => {
        const statusName = (value) => {
          switch (value) {
            case LEAD_STATUS.PENDING:
              return t('status.pending');
            case LEAD_STATUS.IN_PROGRESS:
              return t('status.inProgress');
            case LEAD_STATUS.COMPLETED:
              return t('status.completed');
            case LEAD_STATUS.REJECTED:
              return t('status.rejected');
            case LEAD_STATUS.WRONG_LEAD:
              return t('status.wrongLead');
            case LEAD_STATUS.NOT_QUALIFIED:
              return t('status.notQualified');
            case LEAD_STATUS.NO_COMMUNICATION:
              return t('status.noCommunication');
            case LEAD_STATUS.BOOKED:
              return t('status.booked');
            case LEAD_STATUS.BOOKED_AND_RESERVED:
              return t('status.bookedReserved');
            case LEAD_STATUS.CANCELED:
              return t('status.canceled');
            case LEAD_STATUS.QUOTATION:
              return t('status.quotation')
            case LEAD_STATUS.ASSIGNED:
              return t('status.assigned')
            default:
              return t('status.unknown');
          }
        };

        return (
          row.original.status === LEAD_STATUS.COMPLETED ?
            <span className="completed-status">{statusName(value)}</span> :
            row.original.status === LEAD_STATUS.PENDING ?
              <span className="on-hold-status">{statusName(value)}</span> :
              row.original.status === LEAD_STATUS.IN_PROGRESS ?
                <span className="in-progress-status">{statusName(value)}</span> :
                row.original.status === LEAD_STATUS.REJECTED || row.original.status === LEAD_STATUS.WRONG_LEAD || row.original.status === LEAD_STATUS.NOT_QUALIFIED || row.original.status === LEAD_STATUS.NO_COMMUNICATION ?
                  <span className="not-started-status">{statusName(value)}</span> :
                  <span>{statusName(value)}</span>
        );
      }
    },
    {
      Header: t('tables.headers.createdAt'),
      accessor: "created_at",
      Cell: ({ value, row }) => {
        if (value) {
          const parsedDate = parseISO(value);
          const formattedDate = format(parsedDate, 'yyyy-MM-dd');
          return (
            <>
              <div
                className={`one-line admin-createdat-${row.original.id}`}
                style={{ maxWidth: "160px" }}
              >
                {formattedDate}
              </div>
              <Tooltip
                anchorSelect={`.admin-createdat-${row.original.id}`}
                content={formattedDate}
                className={"bg-dark text-white"}
              />
            </>
          );
        }
        return null;
      },
    }
  ];

  const ticketsColumns = (isLoadingTicketDetails, loadingTicketId, handleViewTicket) => [
    {
      Header: t('tables.headers.id'),
      accessor: "ticketId",
      Cell: ({ row }) => <span>{row.index + 1}</span>,
    },
    {
      Header: t('tables.headers.ticketTitle'),
      accessor: "title",
      Cell: ({ row }) => {
        const title = row.original.title;
        return (
          <>
            <div
              className={`text-nowrap overflow-hidden text-center mx-auto one-line ticket-title-${row.original.id}`}
              style={{ maxWidth: "200px" }}
            >
              {title}
            </div>
            <Tooltip
              anchorSelect={`.ticket-title-${row.original.id}`}
              content={title}
              className={"bg-dark text-white"}
            />
          </>
        )
      },
    },
    {
      Header: t('tables.headers.status'),
      accessor: "status",
      Cell: ({ row }) => {
        const status = row.original.status;

        // Define badge colors based on status
        const getStatusBadgeVariant = (status) => {
          switch (status?.toLowerCase()) {
            case "open":
              return "primary";
            case "in_progress":
              return "warning";
            case "closed":
            case "resolved":
              return "success";
            case "pending":
              return "info";
            case "cancelled":
              return "danger";
            default:
              return "secondary";
          }
        };

        return (
          <div className="text-center">
            <Badge
              bg={getStatusBadgeVariant(status)}
              className="px-3 py-2 d-inline-block"
              style={{ minWidth: "100px" }}
            >
              {(() => {
                // Replace underscores with spaces and capitalize each word
                if (!status) return "";
                return status
                  .replace(/_/g, " ")
                  .split(" ")
                  .map((word) =>
                    word.length > 0
                      ? word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
                      : ""
                  )
                  .join(" ");
              })()}
            </Badge>
          </div>
        );
      },
    },
    {
      Header: t('tables.headers.priority'),
      accessor: "priority",
      Cell: ({ row }) => {
        const priority = row.original.priority;

        // Define badge colors based on priority
        const getPriorityBadgeVariant = (priority) => {
          switch (priority?.toLowerCase()) {
            case "high":
              return "danger";
            case "medium":
              return "warning";
            case "low":
              return "success";
            default:
              return "secondary";
          }
        };

        return (
          <div className="text-center">
            <Badge
              bg={getPriorityBadgeVariant(priority)}
              className="px-3 py-2 d-inline-block"
              style={{ minWidth: "100px" }}
            >
              {priority}
            </Badge>
          </div>
        );
      },
    },
    {
      Header: t('tables.headers.createdAt'),
      accessor: "created_at",
      Cell: ({ value, row }) => {
        if (value) {
          const parsedDate = parseISO(value);
          const formattedDate = format(parsedDate, 'yyyy-MM-dd');
          return (
            <>
              <div className={`one-line ticket-created-${row.index}`} style={{ maxWidth: "160px" }}>
                {formattedDate}
              </div>
              <Tooltip anchorSelect={`.ticket-created-${row.index}`} content={formattedDate} className={"bg-dark text-white"} />
            </>
          );
        }
      },
    },
    {
      Header: t('tables.headers.actions'),
      Cell: ({ row }) => {
        const isThisTicketLoading = isLoadingTicketDetails && loadingTicketId === row.original.id;

        return (
          <div className="d-flex justify-content-center">
            <Button
              variant="info"
              size="sm"
              className="view-ticket-btn"
              disabled={isThisTicketLoading}
              onClick={(e) => {
                e.stopPropagation();
                handleViewTicket(row.original.id);
              }}
            >
              {isThisTicketLoading ? (
                <Spinner animation="border" size="sm" className="me-1" />
              ) : (
                <FaEye className="me-1" />
              )}
              {t('buttons.view')}
            </Button>
          </div>
        );
      }
    }
  ];

  // new leads today table columns for the Admin dashboard
  const newLeadsTodayColumns = [
    {
      Header: t('tables.headers.id') || '#',
      accessor: 'leadId',
      Cell: ({ row }) => <span>{row.index + 1}</span>,
    },
    {
      Header: t('tables.headers.leadName') || 'Name',
      accessor: 'name',
      Cell: ({ row }) => {
        const leadName = row.original.name;
        return (
          <>
            <Link
              to={`/admin/clients/leads/profile/${row.original.id}`}
              className={`one-line newleads-name-${row.original.id}`}
              style={{ maxWidth: "180px" }}
            >
              {leadName}
            </Link>
            <Tooltip
              anchorSelect={`.newleads-name-${row.original.id}`}
              content={leadName}
              className={"bg-dark text-white"}
            />
          </>
        );
      },
    },
    {
      Header: t('tables.headers.clientName') || 'Client',
      accessor: 'client_name',
      Cell: ({ row }) => {
        const clientName = row.original.client_name;
        const clientId = row.original.client_id;
        return (
          <>
            <Link
              to={`/admin/clients/leads/${clientId}`}
              className={`one-line newleads-client-${row.original.id}`}
              style={{ maxWidth: "180px" }}
            >
              {clientName}
            </Link>
            <Tooltip
              anchorSelect={`.newleads-client-${row.original.id}`}
              content={clientName}
              className={"bg-dark text-white"}
            />
          </>
        );
      },
    },
    {
      Header: t('tables.headers.phone'),
      accessor: 'phone',
      Cell: ({ row }) => (
        <>
          <Link
            to={`/admin/clients/leads/profile/${row.original.id}`}
            className={`one-line newleads-phone-${row.original.id}`}
            style={{ maxWidth: "150px" }}
          >
            {row.original.phone}
          </Link>
          <Tooltip
            anchorSelect={`.newleads-phone-${row.original.id}`}
            content={row.original.phone}
            className={"bg-dark text-white"}
          />
        </>
      ),
    },
    {
      Header: t('tables.headers.source'),
      accessor: 'source',
      Cell: ({ row }) => {
        const source = row.original.source;
        const IconComponent = sourceToIcon[source] || null;
        return (
          <Link to={`/admin/clients/leads/profile/${row.original.id}`} className="mx-auto social-icon-container">
            {IconComponent && <ReactSVG src={IconComponent} />}
          </Link>
        );
      },
    },
    {
      Header: t('tables.headers.status') || 'Status',
      accessor: 'status',
      Cell: ({ row }) => {
        const status = row?.original?.status;
        const statusMapping = {
          0: { label: t('status.pending'), className: 'status-badge--pending' },
          1: { label: t('status.inProgress'), className: 'status-badge--in-progress' },
          2: { label: t('status.completed'), className: 'status-badge--completed' },
          3: { label: t('status.rejected'), className: 'status-badge--rejected' },
          4: { label: t('status.wrongLead'), className: 'status-badge--wrong-lead' },
          5: { label: t('status.notQualified'), className: 'status-badge--not-qualified' },
          6: { label: t('status.noCommunication'), className: 'status-badge--no-communication' },
          7: { label: t('status.booked'), className: 'status-badge--booked' },
          8: { label: t('status.bookedReserved'), className: 'status-badge--booked-reserved' },
          9: { label: t('status.canceled'), className: 'status-badge--canceled' },
          10: { label: t('status.quotation'), className: 'status-badge--quotation-sent' },
          11: { label: t('status.assigned'), className: 'status-badge--in-progress' },
        };

        const { label, className } = statusMapping[status] || { label: 'Unknown', className: 'status-badge--unknown' };

        return (
          <Link
            to={`/admin/clients/leads/profile/${row.original.id}`}
            className={`status-badge ${className} rounded-pill p-1`}
            style={{ fontSize: '0.8rem', fontWeight: 600 }}
          >
            {label}
          </Link>
        );
      },
    },

  ];

  // completed leads today table columns (admin dashboard)
  const completedLeadsTodayColumns = [
    {
      Header: t('tables.headers.id') || '#',
      accessor: 'leadId',
      Cell: ({ row }) => <span>{row.index + 1}</span>,
    },
    {
      Header: t('leadsTable.columns.leadName') || 'Name',
      accessor: 'name',
      Cell: ({ row }) => (
        <>
          <Link
            to={`/admin/clients/leads/profile/${row.original.id}`}
            className={`one-line completed-name-${row.original.id}`}
            style={{ maxWidth: "180px" }}
          >
            {row.original.name}
          </Link>
          <Tooltip
            anchorSelect={`.completed-name-${row.original.id}`}
            content={row.original.name}
            className={"bg-dark text-white"}
          />
        </>
      ),
    },
    {
      Header: t('leadsTable.columns.clientName') || 'Client',
      accessor: 'client_name',
      Cell: ({ row }) => {
        const clientName = row.original.client_name;
        const clientId = row.original.client_id;
        return (
          <>
            <Link
              to={`/admin/clients/leads/${clientId}`}
              className={`one-line completed-client-${row.original.id}`}
              style={{ maxWidth: "180px" }}
            >
              {clientName}
            </Link>
            <Tooltip
              anchorSelect={`.completed-client-${row.original.id}`}
              content={clientName}
              className={"bg-dark text-white"}
            />
          </>
        );
      },
    },
    {
      Header: t('leadsTable.columns.phone'),
      accessor: 'phone',
      Cell: ({ row }) => (
        <>
          <Link
            to={`/admin/clients/leads/profile/${row.original.id}`}
            className={`one-line completed-phone-${row.original.id}`}
            style={{ maxWidth: "150px" }}
          >
            {row.original.phone}
          </Link>
          <Tooltip
            anchorSelect={`.completed-phone-${row.original.id}`}
            content={row.original.phone}
            className={"bg-dark text-white"}
          />
        </>
      ),
    },
    {
      Header: t('leadsTable.columns.assignedTo') || 'Assigned To',
      accessor: 'assignedTo',
      Cell: ({ row }) => {
        const assignedTo = row.original.assignedTo;
        return <Link to={`/admin/clients/leads/profile/${row.original.id}`}>{assignedTo?.name}</Link>;
      },
    },
    {
      Header: t('leadsTable.columns.source'),
      accessor: 'source',
      Cell: ({ row }) => {
        const source = row.original.source;
        const IconComponent = sourceToIcon[source] || null;
        return (
          <Link to={`/admin/clients/leads/profile/${row.original.id}`} className='mx-auto social-icon-container'>
            {IconComponent && <ReactSVG src={IconComponent} />}
          </Link>
        );
      },
    },
    {
      Header: t('leadsTable.columns.createdAt') || 'Created At',
      accessor: 'createdAt',
      Cell: ({ row, value }) => {
        let display = value;
        if (value) {
          const parsedDate = new Date(value);
          if (!isNaN(parsedDate.getTime())) {
            display = format(parsedDate, 'yyyy-MM-dd HH:mm:ss');
          }
        }
        const classKey = `completed-created-${row.original.id}`;
        return (
          <>
            <Link
              to={`/admin/clients/leads/profile/${row.original.id}`}
              className={`one-line ${classKey}`}
              style={{ maxWidth: "180px" }}
            >
              {display || '-'}
            </Link>
            {display && (
              <Tooltip anchorSelect={`.${classKey}`} content={display} className={"bg-dark text-white"} />
            )}
          </>
        );
      },
    },
  ];

  /*
   * Filter Table Leads Columns (All Clients Tab)
   * Accepts an options object for interactive controls that depend on the parent component.
   */
  const filterTableLeadsColumns = ({
    filteredTeamMembers = [],
    currentUserPermissions = [],
    dispatch,
    handleAssignTeamMemberThunk,
    handleDelete,
    isHoverSupported = true,
    handleTouchStart = () => { },
    tooltipVisible = {},
    user = null,
  } = {}) => [
      {
        Header: t('leadsTable.columns.id'),
        accessor: 'clientId',
        Cell: () => null, // hidden index column
      },
      {
        id: 'updatedAt',
        accessor: 'updatedAt',
        Header: 'Updated At',
        Cell: () => null,
      },
      {
        Header: t('leadsTable.columns.contactName'),
        accessor: 'contactName',
        Cell: ({ row }) => {
          const name = row.original.contactName;
          const unassigned = row.original.assignedTo === null || row.original.assignedTo === undefined;
          const leadId = row.original.id;
          return (
            <>
              <Link
                to={`/leads/${leadId}`}
                className={`d-flex align-items-center gap-1 justify-content-center one-line filter-contact-${leadId}`}
                style={{ maxWidth: '200px' }}
              >
                {unassigned && <IoSparklesSharp size={18} color="#92C020" />} {name}
              </Link>
              <Tooltip
                anchorSelect={`.filter-contact-${leadId}`}
                content={name}
                className={"bg-dark text-white"}
              />
            </>
          );
        },
      },
      {
        Header: t('leadsTable.columns.phone'),
        accessor: 'phone',
        Cell: ({ row }) => {
          const phone = row.original.phone;
          return (
            <>
              <Link
                to={`/leads/${row.original.id}`}
                className={`one-line filter-phone-${row.original.id}`}
                style={{ maxWidth: "150px" }}
              >
                {phone}
              </Link>
              <Tooltip
                anchorSelect={`.filter-phone-${row.original.id}`}
                content={phone}
                className={"bg-dark text-white"}
              />
            </>
          );
        },
      },
      {
        Header: t('leadsTable.columns.assignedTo'),
        accessor: 'assignedTo',
        Cell: ({ row }) => {
          const assignedTo = row.original.assignedTo;

          if (assignedTo) return <span>{assignedTo}</span>;

          return (
            <Dropdown>
              <Dropdown.Toggle variant="light" id="assign-dropdown" className="team-actions-button p-0 rounded-3">
                <div className="assign-client-icon m-0">
                  <FaUserPlus size={20} />
                </div>
              </Dropdown.Toggle>
              <Dropdown.Menu className="team-actions-menu" container="body">
                {filteredTeamMembers?.length > 0 ? (
                  filteredTeamMembers.map((member) => (
                    <Dropdown.Item
                      key={member.id}
                      onClick={() => dispatch && dispatch(handleAssignTeamMemberThunk({ leadId: row.original.id, memberId: member.id }))}
                    >
                      {member.name}
                    </Dropdown.Item>
                  ))
                ) : (
                  <Dropdown.Item disabled>{t('leadsTable.columns.noTeamMembers')}</Dropdown.Item>
                )}
              </Dropdown.Menu>
            </Dropdown>
          );
        },
      },
      {
        Header: t('leadsTable.columns.source'),
        accessor: 'source',
        Cell: ({ row }) => {
          const src = row.original.source;
          const IconComponent = sourceToIcon[src] || null;
          return (
            <Link to={`/leads/${row.original.id}`} className="mx-auto social-icon-container">
              {IconComponent && <ReactSVG src={IconComponent} />}
            </Link>
          );
        },
      },
      {
        Header: t('leadsTable.columns.status'),
        accessor: 'status',
        Cell: ({ row }) => {
          const status = row.original.status;
          const statusMapping = {
            0: { label: t('status.pending'), className: 'status-badge--pending' },
            1: { label: t('status.inProgress'), className: 'status-badge--in-progress' },
            2: { label: t('status.completed'), className: 'status-badge--completed' },
            3: { label: t('status.rejected'), className: 'status-badge--rejected' },
            4: { label: t('status.wrongLead'), className: 'status-badge--wrong-lead' },
            5: { label: t('status.notQualified'), className: 'status-badge--not-qualified' },
            6: { label: t('status.noCommunication'), className: 'status-badge--no-communication' },
            7: { label: t('status.booked'), className: 'status-badge--booked' },
            8: { label: t('status.bookedReserved'), className: 'status-badge--booked-reserved' },
            9: { label: t('status.canceled'), className: 'status-badge--canceled' },
            10: { label: t('status.quotation'), className: 'status-badge--quotation-sent' },
            11: { label: t('status.assigned'), className: 'status-badge--in-progress' },
          };

          const { label, className } = statusMapping[status] || { label: 'Unknown', className: 'status-badge--unknown' };

          return (
            <Link to={`/leads/${row.original.id}`} className={`status-badge ${className} rounded-pill p-1`} style={{ fontSize: '0.8rem', fontWeight: 600 }}>
              {label}
            </Link>
          );
        },
      },
      {
        Header: t('leadsTable.columns.service'),
        accessor: 'service',
        Cell: ({ row }) => {
          const serviceName = row.original.service;
          const leadId = row.original.id;
          return (
            <>
              <Link
                to={`/leads/${leadId}`}
                className={`one-line filter-service-${leadId}`}
                style={{ maxWidth: '150px' }}
              >
                {serviceName}
              </Link>
              <Tooltip
                anchorSelect={`.filter-service-${leadId}`}
                content={serviceName}
                className={"bg-dark text-white"}
              />
            </>
          );
        },
      },
      {
        Header: t('leadsTable.columns.pageName'),
        accessor: 'pageName',
        Cell: ({ row }) => {
          const pageName = row.original.pageName;
          const leadId = row.original.id;
          return (
            <>
              <Link
                to={`/leads/${leadId}`}
                className={`one-line filter-page-${leadId}`}
                style={{ maxWidth: '150px' }}
              >
                {pageName}
              </Link>
              <Tooltip
                anchorSelect={`.filter-page-${leadId}`}
                content={pageName}
                className={"bg-dark text-white"}
              />
            </>
          );
        },
      },
      {
        Header: t('leadsTable.columns.lastActivity'),
        accessor: 'lastActivity',
        Cell: ({ row }) => {
          const logs = Array.isArray(row.original.logs) ? row.original.logs.slice(-3) : [row.original.logs];
          const lastLog = logs[0];
          if (!lastLog) return null;

          const leadId = row.original.id;
          const tooltipId = `lastActivity_${leadId}`;

          return (
            <Link to={`/leads/${leadId}`} data-tooltip-id={tooltipId}>
              <div className="activity-button">
                {lastLog?.action === 1 && <MdAssignmentTurnedIn size={20} />}
                {lastLog?.action === 2 && <FaPhone size={20} />}
                {lastLog?.action === 3 && <FaCalendar size={20} />}
                {lastLog?.action === 4 && <FaEnvelope size={20} />}
              </div>
              <Tooltip id={tooltipId} className="logs-tooltip-container" content={
                <div className="logs-list-container">
                  {logs.map((log, idx) => (
                    <div key={idx} className="log-container">
                      <div className="d-flex justify-content-center my-2">
                        {log?.action === 1 && <MdAssignmentTurnedIn size={20} className="mainColor" />}
                        {log?.action === 2 && <FaPhone size={20} className="mainColor" />}
                        {log?.action === 3 && <FaCalendar size={20} className="mainColor" />}
                        {log?.action === 4 && <FaEnvelope size={20} className="mainColor" />}
                      </div>
                      <p className="opacity-50">{log?.result || log?.note}</p>
                    </div>
                  ))}
                </div>
              } place="left-start" events={["hover"]} />
            </Link>
          );
        },
      },
      {
        Header: t('leadsTable.columns.createdAt'),
        accessor: 'createdAt',
        Cell: ({ value, row }) => {
          const classKey = `filter-created-${row.original.id}`;
          let display = value;
          if (value) {
            const parsedDate = new Date(value);
            if (!isNaN(parsedDate.getTime())) {
              display = format(parsedDate, 'yyyy-MM-dd HH:mm:ss');
            }
          }
          return (
            <>
              <Link
                to={`/leads/${row.original.id}`}
                className={`one-line ${classKey}`}
                style={{ maxWidth: "180px" }}
              >
                {display}
              </Link>
              <Tooltip anchorSelect={`.${classKey}`} content={display} className={"bg-dark text-white"} />
            </>
          );
        },
      },
      {
        Header: t('leadsTable.columns.actions'),
        Cell: ({ row }) => (
          <div className="d-flex justify-content-center">
            {currentUserPermissions?.includes('lead-edit') && (
              <Link to={`/leads/${row.original.id}`} className="me-3 shadow-sm rounded-2 p-1">
                <MdEdit size={20} className="text-dark" />
              </Link>
            )}
            {currentUserPermissions?.includes('lead-delete') && (
              <div className="shadow-sm rounded-2 p-1">
                <PiTrashFill size={20} className="text-danger" onClick={() => handleDelete && handleDelete(row.original.id)} />
              </div>
            )}
          </div>
        ),
      },
    ];

  // Ads table columns
  const adsColumns = (handleViewLeads) => [
    {
      Header: t('adsTable.columns.id'),
      accessor: 'ad_id',
      Cell: ({ row }) => (
        <div className="text-center">
          <div
            onClick={() => handleViewLeads(row.original)}
            style={{ cursor: 'pointer' }}
          >
            {row.index + 1}
          </div>
        </div>
      ),
    },
    {
      Header: t('adsTable.columns.adName'),
      accessor: 'ad_name',
      Cell: ({ row }) => {
        const adName = row.original.ad_name;
        return (
          <div className="text-center">
            <div
              onClick={() => handleViewLeads(row.original)}
              className={`one-line ad-name-${row.original.ad_id} mx-auto`}
              style={{
                maxWidth: "200px",
                cursor: 'pointer'
              }}
            >
              {adName}
            </div>
            <Tooltip
              anchorSelect={`.ad-name-${row.original.ad_id}`}
              content={adName}
              className={"bg-dark text-white"}
            />
          </div>
        );
      },
    },
    {
      Header: t('adsTable.columns.campaignName'),
      accessor: 'campaign_name',
      Cell: ({ row }) => {
        const campaignName = row.original.campaign_name;
        return (
          <div className="text-center">
            <div
              onClick={() => handleViewLeads(row.original)}
              className={`one-line campaign-name-${row.original.ad_id} mx-auto`}
              style={{
                maxWidth: "180px",
                cursor: 'pointer'
              }}
            >
              {campaignName}
            </div>
            <Tooltip
              anchorSelect={`.campaign-name-${row.original.ad_id}`}
              content={campaignName}
              className={"bg-dark text-white"}
            />
          </div>
        );
      },
    },
    {
      Header: t('adsTable.columns.pageName'),
      accessor: 'page_name',
      Cell: ({ row }) => {
        const pageName = row.original.page_name;
        return (
          <div className="text-center">
            <div
              onClick={() => handleViewLeads(row.original)}
              className={`one-line page-name-${row.original.ad_id} mx-auto`}
              style={{
                maxWidth: "150px",
                cursor: 'pointer'
              }}
            >
              {pageName}
            </div>
            <Tooltip
              anchorSelect={`.page-name-${row.original.ad_id}`}
              content={pageName}
              className={"bg-dark text-white"}
            />
          </div>
        );
      },
    },
    {
      Header: t('adsTable.columns.leadsCount'),
      accessor: 'leads_count',
      Cell: ({ row }) => (
        <div className="text-center">
          <div
            onClick={() => handleViewLeads(row.original)}
            className="fw-bold text-primary"
            style={{ cursor: 'pointer' }}
          >
            {row.original.leads_count}
          </div>
        </div>
      ),
    },
    {
      Header: t('adsTable.columns.actions'),
      Cell: ({ row }) => (
        <div className="d-flex justify-content-center">
          <Button
            size="sm"
            className="submit-btn px-2 py-2"
            onClick={() => handleViewLeads(row.original)}
          >
            <BsEye className="me-1" />
            {t('buttons.view')}
          </Button>
        </div>
      ),
    },
  ];

  // Return all column configurations
  return {
    leadAssignmentColumns,
    teamMembersColumns,
    salesColumns,
    statisticsForTMColumns,
    newLeadsForAdmin,
    newLeadsTodayColumns,
    completedLeadsTodayColumns,
    ticketsColumns,
    filterTableLeadsColumns,
    adsColumns,
  };
};

// Hook to access translated columns easily
export const useTranslatedColumns = (user = null) => {
  const { t } = useTranslation();
  return useMemo(() => createTranslatedColumns(t, user), [t, user]);
};
