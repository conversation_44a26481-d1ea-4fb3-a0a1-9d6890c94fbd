/**
 * Tests for Firebase Query Optimization Service
 */

import {
    fetchOptimizedMessages,
    fetchOptimizedComments,
    fetchChatDataOptimized,
    listenToMessagesOptimized,
    listenToCommentsOptimized,
    listenToWhatsAppMessagesOptimized,
    getQueryPerformanceStats,
    clearQueryCache
} from './queryOptimization';

// Mock Firebase
jest.mock('../../utils/firebase.config', () => ({
    db: {}
}));

jest.mock('firebase/firestore', () => ({
    collection: jest.fn(),
    query: jest.fn(),
    where: jest.fn(),
    orderBy: jest.fn(),
    limit: jest.fn(),
    startAfter: jest.fn(),
    getDocs: jest.fn(),
    onSnapshot: jest.fn(),
    collectionGroup: jest.fn(),
    doc: jest.fn(),
    getDoc: jest.fn(),
    enableNetwork: jest.fn(),
    disableNetwork: jest.fn()
}));

// Mock collection paths service
jest.mock('./collectionPaths', () => ({
    getCollectionPaths: jest.fn(),
    getChatIdentifier: jest.fn(),
    determineChatType: jest.fn()
}));

import { getCollectionPaths } from './collectionPaths';
import { collection, query, orderBy, limit, getDocs, onSnapshot, collectionGroup } from 'firebase/firestore';

describe('Firebase Query Optimization Service', () => {
    beforeEach(() => {
        jest.clearAllMocks();
        clearQueryCache();

        // Setup default mocks
        getCollectionPaths.mockReturnValue({
            messages: 'chats/test-chat/messages',
            comments: 'chats/test-chat/comments'
        });

        collection.mockReturnValue({ _type: 'collection' });
        query.mockReturnValue({ _type: 'query' });
        orderBy.mockReturnValue({ _type: 'orderBy' });
        limit.mockReturnValue({ _type: 'limit' });
    });

    describe('fetchOptimizedMessages', () => {
        it('should fetch messages with performance monitoring', async () => {
            const mockDocs = [
                {
                    id: 'msg1',
                    data: () => ({ message: 'Hello', created_time: '2023-01-01T00:00:00Z' }),
                    ref: { _type: 'docRef' }
                }
            ];

            getDocs.mockResolvedValue({
                docs: mockDocs
            });

            const result = await fetchOptimizedMessages('test-chat', 'messenger', {
                limit: 10,
                useCache: false
            });

            expect(result).toHaveLength(1);
            expect(result[0]).toEqual({
                id: 'msg1',
                message: 'Hello',
                created_time: '2023-01-01T00:00:00Z',
                _docRef: { _type: 'docRef' }
            });

            expect(getCollectionPaths).toHaveBeenCalledWith('test-chat', 'messenger');
            expect(collection).toHaveBeenCalled();
            expect(query).toHaveBeenCalled();
            expect(orderBy).toHaveBeenCalledWith('created_time', 'asc');
            expect(limit).toHaveBeenCalledWith(10);
        });

        it('should use cache when enabled', async () => {
            const mockDocs = [
                {
                    id: 'msg1',
                    data: () => ({ message: 'Hello', created_time: '2023-01-01T00:00:00Z' }),
                    ref: { _type: 'docRef' }
                }
            ];

            getDocs.mockResolvedValue({
                docs: mockDocs
            });

            // First call should hit Firebase
            const result1 = await fetchOptimizedMessages('test-chat', 'messenger', {
                limit: 10,
                useCache: true
            });

            // Second call should use cache
            const result2 = await fetchOptimizedMessages('test-chat', 'messenger', {
                limit: 10,
                useCache: true
            });

            expect(result1).toEqual(result2);
            expect(getDocs).toHaveBeenCalledTimes(1); // Only called once due to caching
        });

        it('should handle errors gracefully', async () => {
            getDocs.mockRejectedValue(new Error('Firebase error'));

            await expect(fetchOptimizedMessages('test-chat', 'messenger'))
                .rejects.toThrow('Firebase error');
        });

        it('should throw error for invalid collection path', async () => {
            getCollectionPaths.mockReturnValue({
                messages: null,
                comments: 'chats/test-chat/comments'
            });

            await expect(fetchOptimizedMessages('test-chat', 'messenger'))
                .rejects.toThrow('Unable to determine messages collection path');
        });
    });

    describe('fetchOptimizedComments', () => {
        it('should fetch comments with performance monitoring', async () => {
            const mockDocs = [
                {
                    id: 'comment1',
                    data: () => ({ text: 'Great!', createdAt: '2023-01-01T00:00:00Z' }),
                    ref: { _type: 'docRef' }
                }
            ];

            getDocs.mockResolvedValue({
                docs: mockDocs
            });

            const result = await fetchOptimizedComments('test-chat', 'messenger', {
                limit: 20,
                useCache: false
            });

            expect(result).toHaveLength(1);
            expect(result[0]).toEqual({
                id: 'comment1',
                text: 'Great!',
                createdAt: '2023-01-01T00:00:00Z',
                _docRef: { _type: 'docRef' }
            });

            expect(orderBy).toHaveBeenCalledWith('createdAt', 'asc');
            expect(limit).toHaveBeenCalledWith(20);
        });
    });

    describe('fetchChatDataOptimized', () => {
        it('should fetch both messages and comments in parallel', async () => {
            const mockMessageDocs = [
                {
                    id: 'msg1',
                    data: () => ({ message: 'Hello', created_time: '2023-01-01T00:00:00Z' }),
                    ref: { _type: 'docRef' }
                }
            ];

            const mockCommentDocs = [
                {
                    id: 'comment1',
                    data: () => ({ text: 'Great!', createdAt: '2023-01-01T00:00:00Z' }),
                    ref: { _type: 'docRef' }
                }
            ];

            getDocs
                .mockResolvedValueOnce({ docs: mockMessageDocs })
                .mockResolvedValueOnce({ docs: mockCommentDocs });

            const result = await fetchChatDataOptimized('test-chat', 'messenger', {
                messagesLimit: 10,
                commentsLimit: 20,
                useCache: false
            });

            expect(result.messages).toHaveLength(1);
            expect(result.comments).toHaveLength(1);
            expect(result.messages[0].message).toBe('Hello');
            expect(result.comments[0].text).toBe('Great!');
        });
    });

    describe('listenToMessagesOptimized', () => {
        it('should set up real-time listener with performance monitoring', () => {
            const mockUnsubscribe = jest.fn();
            const mockCallback = jest.fn();

            onSnapshot.mockImplementation((query, successCallback, errorCallback) => {
                // Simulate successful snapshot
                const mockSnapshot = {
                    docs: [
                        {
                            id: 'msg1',
                            data: () => ({ message: 'Hello', created_time: '2023-01-01T00:00:00Z' }),
                            ref: { _type: 'docRef' }
                        }
                    ],
                    docChanges: () => [{ type: 'added' }]
                };

                successCallback(mockSnapshot);
                return mockUnsubscribe;
            });

            const unsubscribe = listenToMessagesOptimized('test-chat', 'messenger', mockCallback, {
                limit: 10
            });

            expect(mockCallback).toHaveBeenCalledWith([
                {
                    id: 'msg1',
                    message: 'Hello',
                    created_time: '2023-01-01T00:00:00Z',
                    _docRef: { _type: 'docRef' }
                }
            ]);

            expect(typeof unsubscribe).toBe('function');
            expect(onSnapshot).toHaveBeenCalled();
        });

        it('should handle listener errors', () => {
            const mockCallback = jest.fn();

            onSnapshot.mockImplementation((query, successCallback, errorCallback) => {
                errorCallback(new Error('Listener error'));
                return jest.fn();
            });

            listenToMessagesOptimized('test-chat', 'messenger', mockCallback);

            expect(mockCallback).toHaveBeenCalledWith([], expect.any(Error));
        });
    });

    describe('listenToWhatsAppMessagesOptimized', () => {
        it('should set up collection group listener for WhatsApp messages', () => {
            const mockCallback = jest.fn();
            const mockUnsubscribe1 = jest.fn();
            const mockUnsubscribe2 = jest.fn();

            collectionGroup.mockReturnValue({ _type: 'collectionGroup' });

            onSnapshot
                .mockImplementationOnce((query, successCallback) => {
                    // Simulate incoming messages
                    const mockSnapshot = {
                        docChanges: () => [
                            {
                                type: 'added',
                                doc: {
                                    id: 'msg1',
                                    data: () => ({
                                        message: 'Hello',
                                        created_time: '2023-01-01T00:00:00Z',
                                        sender: '+1234567890',
                                        recipient: '+0987654321'
                                    }),
                                    ref: {
                                        parent: {
                                            parent: {
                                                id: '+1234567890'
                                            }
                                        }
                                    }
                                }
                            }
                        ]
                    };

                    successCallback(mockSnapshot);
                    return mockUnsubscribe1;
                })
                .mockImplementationOnce((query, successCallback) => {
                    // Simulate outgoing messages
                    const mockSnapshot = {
                        docChanges: () => []
                    };

                    successCallback(mockSnapshot);
                    return mockUnsubscribe2;
                });

            const unsubscribe = listenToWhatsAppMessagesOptimized('+0987654321', mockCallback, {
                limit: 50
            });

            expect(collectionGroup).toHaveBeenCalledWith({}, 'messages');
            expect(mockCallback).toHaveBeenCalled();
            expect(typeof unsubscribe).toBe('function');

            // Test unsubscribe calls both listeners
            unsubscribe();
            expect(mockUnsubscribe1).toHaveBeenCalled();
            expect(mockUnsubscribe2).toHaveBeenCalled();
        });
    });

    describe('Performance monitoring', () => {
        it('should track query performance statistics', async () => {
            const mockDocs = [
                {
                    id: 'msg1',
                    data: () => ({ message: 'Hello', created_time: '2023-01-01T00:00:00Z' }),
                    ref: { _type: 'docRef' }
                }
            ];

            getDocs.mockResolvedValue({
                docs: mockDocs
            });

            await fetchOptimizedMessages('test-chat', 'messenger', { useCache: false });

            const stats = getQueryPerformanceStats();
            expect(stats).toHaveProperty('activeQueries');
            expect(stats).toHaveProperty('cacheSize');
            expect(stats).toHaveProperty('cacheHitRate');
        });
    });

    describe('Cache management', () => {
        it('should clear cache when requested', async () => {
            const mockDocs = [
                {
                    id: 'msg1',
                    data: () => ({ message: 'Hello', created_time: '2023-01-01T00:00:00Z' }),
                    ref: { _type: 'docRef' }
                }
            ];

            getDocs.mockResolvedValue({
                docs: mockDocs
            });

            // First call to populate cache
            await fetchOptimizedMessages('test-chat', 'messenger', { useCache: true });

            // Clear cache
            clearQueryCache();

            // Second call should hit Firebase again
            await fetchOptimizedMessages('test-chat', 'messenger', { useCache: true });

            expect(getDocs).toHaveBeenCalledTimes(2);
        });
    });

    describe('Error handling', () => {
        it('should handle invalid chat ID gracefully', async () => {
            getCollectionPaths.mockReturnValue({
                messages: null,
                comments: null
            });

            await expect(fetchOptimizedMessages(null, 'messenger'))
                .rejects.toThrow('Unable to determine messages collection path');
        });

        it('should handle Firebase connection errors', async () => {
            getDocs.mockRejectedValue(new Error('Network error'));

            await expect(fetchOptimizedMessages('test-chat', 'messenger'))
                .rejects.toThrow('Network error');
        });
    });
});
