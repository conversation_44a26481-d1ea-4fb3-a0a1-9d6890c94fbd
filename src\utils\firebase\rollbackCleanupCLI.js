/**
 * CLI Utility for Firebase Rollback and Cleanup Operations
 *
 * This utility provides command-line interface for performing rollback and cleanup
 * operations on Firebase collections during the migration process.
 */

import {
    rollbackToChatIdStructure,
    cleanupOldSenderIdPaths,
    getComprehensiveStatus
} from './rollbackCleanup';
import { getMigrationStatus } from './dataMigration';

/**
 * CLI Commands for rollback and cleanup operations
 */
export class RollbackCleanupCLI {
    constructor() {
        this.operations = {
            'rollback': this.performRollback.bind(this),
            'cleanup': this.performCleanup.bind(this),
            'status': this.getStatus.bind(this),
            'help': this.showHelp.bind(this)
        };
    }

    /**
     * Execute CLI command
     * @param {string} command - Command to execute
     * @param {Object} selectedChat - Chat object
     * @param {Object} options - Command options
     */
    async execute(command, selectedChat, options = {}) {
        const operation = this.operations[command];
        if (!operation) {
            console.error(`Unknown command: ${command}`);
            this.showHelp();
            return { success: false, error: `Unknown command: ${command}` };
        }

        try {
            return await operation(selectedChat, options);
        } catch (error) {
            console.error(`Error executing ${command}:`, error);
            return { success: false, error: error.message };
        }
    }

    /**
     * Perform rollback operation
     */
    async performRollback(selectedChat, options = {}) {
        const {
            dryRun = false,
            deleteSource = false,
            batchSize = 50,
            verifyIntegrity = true,
            force = false
        } = options;

        console.log('\n=== FIREBASE COLLECTION ROLLBACK ===');
        console.log(`Chat ID: ${selectedChat?.id}`);
        console.log(`Dry Run: ${dryRun}`);
        console.log(`Delete Source: ${deleteSource}`);
        console.log(`Verify Integrity: ${verifyIntegrity}`);
        console.log(`Batch Size: ${batchSize}`);

        if (!force && !dryRun) {
            console.log('\n⚠️  WARNING: This operation will rollback messages from chat ID structure to sender ID structure.');
            console.log('This should only be done in emergency situations.');
            console.log('Use --force flag to proceed or --dry-run to simulate.');
            return { success: false, error: 'Operation requires --force flag or --dry-run' };
        }

        // Get current status
        console.log('\n📊 Checking current status...');
        const status = await getComprehensiveStatus(selectedChat);
        console.log(`Status: ${status.status}`);
        console.log(`Legacy Count: ${status.legacyCount || 0}`);
        console.log(`Current Count: ${status.currentCount || 0}`);
        console.log(`Can Rollback: ${status.canRollback ? 'Yes' : 'No'}`);

        if (!status.canRollback) {
            console.log('\n❌ Rollback not possible: No messages in current (chat ID) structure');
            return { success: false, error: 'No messages to rollback' };
        }

        // Perform rollback
        console.log('\n🔄 Starting rollback operation...');
        const startTime = Date.now();

        const result = await rollbackToChatIdStructure(selectedChat, {
            dryRun,
            deleteSource,
            batchSize,
            verifyIntegrity
        });

        const duration = Date.now() - startTime;

        // Display results
        console.log('\n📋 ROLLBACK RESULTS:');
        console.log(`Success: ${result.success ? '✅' : '❌'}`);
        console.log(`Rolled Back: ${result.rolledBackCount}`);
        console.log(`Skipped: ${result.skippedCount}`);
        console.log(`Errors: ${result.errorCount}`);
        console.log(`Duration: ${duration}ms`);
        console.log(`From Path: ${result.fromPath}`);
        console.log(`To Path: ${result.toPath}`);

        if (result.errors.length > 0) {
            console.log('\n❌ ERRORS:');
            result.errors.forEach((error, index) => {
                console.log(`${index + 1}. ${error.type}: ${error.message}`);
            });
        }

        if (dryRun) {
            console.log('\n💡 This was a dry run. No actual changes were made.');
            console.log('Remove --dry-run flag to perform actual rollback.');
        } else if (result.success) {
            console.log('\n✅ Rollback completed successfully!');
            if (deleteSource) {
                console.log('Source messages were deleted as requested.');
            }
        }

        return result;
    }

    /**
     * Perform cleanup operation
     */
    async performCleanup(selectedChat, options = {}) {
        const {
            dryRun = false,
            batchSize = 50,
            verifyMigration = true,
            force = false
        } = options;

        console.log('\n=== FIREBASE COLLECTION CLEANUP ===');
        console.log(`Chat ID: ${selectedChat?.id}`);
        console.log(`Dry Run: ${dryRun}`);
        console.log(`Verify Migration: ${verifyMigration}`);
        console.log(`Batch Size: ${batchSize}`);

        if (!force && !dryRun) {
            console.log('\n⚠️  WARNING: This operation will permanently delete old sender ID-based messages.');
            console.log('Ensure migration is complete and verified before proceeding.');
            console.log('Use --force flag to proceed or --dry-run to simulate.');
            return { success: false, error: 'Operation requires --force flag or --dry-run' };
        }

        // Get current status
        console.log('\n📊 Checking current status...');
        const status = await getComprehensiveStatus(selectedChat);
        console.log(`Status: ${status.status}`);
        console.log(`Legacy Count: ${status.legacyCount || 0}`);
        console.log(`Current Count: ${status.currentCount || 0}`);
        console.log(`Can Cleanup: ${status.canCleanup ? 'Yes' : 'No'}`);

        if (!status.canCleanup) {
            console.log('\n❌ Cleanup not possible: No old messages to clean or migration not complete');
            return { success: false, error: 'No messages to cleanup or migration incomplete' };
        }

        if (status.recommendedAction !== 'cleanup') {
            console.log(`\n⚠️  Current status suggests: ${status.recommendedAction}`);
            if (!force) {
                console.log('Use --force flag to proceed anyway.');
                return { success: false, error: 'Status does not recommend cleanup' };
            }
        }

        // Perform cleanup
        console.log('\n🧹 Starting cleanup operation...');
        const startTime = Date.now();

        const result = await cleanupOldSenderIdPaths(selectedChat, {
            dryRun,
            batchSize,
            verifyMigration
        });

        const duration = Date.now() - startTime;

        // Display results
        console.log('\n📋 CLEANUP RESULTS:');
        console.log(`Success: ${result.success ? '✅' : '❌'}`);
        console.log(`Deleted: ${result.deletedCount}`);
        console.log(`Errors: ${result.errorCount}`);
        console.log(`Duration: ${duration}ms`);
        console.log(`Cleaned Path: ${result.cleanedPath}`);

        if (result.errors.length > 0) {
            console.log('\n❌ ERRORS:');
            result.errors.forEach((error, index) => {
                console.log(`${index + 1}. ${error.type}: ${error.message}`);
            });
        }

        if (dryRun) {
            console.log('\n💡 This was a dry run. No actual changes were made.');
            console.log('Remove --dry-run flag to perform actual cleanup.');
        } else if (result.success) {
            console.log('\n✅ Cleanup completed successfully!');
            console.log('Old sender ID-based messages have been removed.');
        }

        return result;
    }

    /**
     * Get comprehensive status
     */
    async getStatus(selectedChat, options = {}) {
        const { detailed = false } = options;

        console.log('\n=== FIREBASE COLLECTION STATUS ===');
        console.log(`Chat ID: ${selectedChat?.id}`);

        try {
            // Get comprehensive status
            const status = await getComprehensiveStatus(selectedChat);

            console.log('\n📊 CURRENT STATUS:');
            console.log(`Requires Operations: ${status.requiresOperations ? 'Yes' : 'No'}`);
            console.log(`Chat Type: ${status.chatType || 'Unknown'}`);
            console.log(`Status: ${status.status}`);
            console.log(`Recommended Action: ${status.recommendedAction}`);

            if (status.requiresOperations) {
                console.log(`Legacy Path: ${status.legacyPath}`);
                console.log(`Current Path: ${status.currentPath}`);
                console.log(`Legacy Count: ${status.legacyCount || 0}`);
                console.log(`Current Count: ${status.currentCount || 0}`);
                console.log(`Can Rollback: ${status.canRollback ? 'Yes' : 'No'}`);
                console.log(`Can Cleanup: ${status.canCleanup ? 'Yes' : 'No'}`);
            }

            if (detailed) {
                console.log('\n📋 DETAILED MIGRATION STATUS:');
                const migrationStatus = await getMigrationStatus(selectedChat);
                console.log(`Migration Required: ${migrationStatus.requiresMigration ? 'Yes' : 'No'}`);
                console.log(`Migration Status: ${migrationStatus.status}`);
                if (migrationStatus.sourceCount !== undefined) {
                    console.log(`Source Messages: ${migrationStatus.sourceCount}`);
                    console.log(`Destination Messages: ${migrationStatus.destCount}`);
                }
            }

            // Provide recommendations
            console.log('\n💡 RECOMMENDATIONS:');
            switch (status.recommendedAction) {
                case 'migrate':
                    console.log('• Run migration to move messages from sender ID to chat ID structure');
                    break;
                case 'cleanup':
                    console.log('• Run cleanup to remove old sender ID-based messages');
                    console.log('• Ensure migration is verified before cleanup');
                    break;
                case 'none':
                    console.log('• No action required');
                    break;
                default:
                    console.log('• Check individual operations for specific actions');
            }

            if (status.error) {
                console.log(`\n❌ ERROR: ${status.error}`);
            }

            return { success: true, status };

        } catch (error) {
            console.error('\n❌ Error getting status:', error);
            return { success: false, error: error.message };
        }
    }

    /**
     * Show help information
     */
    showHelp() {
        console.log('\n=== FIREBASE ROLLBACK & CLEANUP CLI ===');
        console.log('\nAvailable Commands:');
        console.log('  rollback    - Rollback messages from chat ID to sender ID structure');
        console.log('  cleanup     - Clean up old sender ID-based messages after migration');
        console.log('  status      - Show current migration and cleanup status');
        console.log('  help        - Show this help message');

        console.log('\nRollback Options:');
        console.log('  --dry-run           - Simulate rollback without making changes');
        console.log('  --delete-source     - Delete chat ID messages after rollback');
        console.log('  --batch-size=N      - Process N messages per batch (default: 50)');
        console.log('  --verify-integrity  - Verify data integrity before rollback (default: true)');
        console.log('  --force             - Force rollback without confirmation');

        console.log('\nCleanup Options:');
        console.log('  --dry-run           - Simulate cleanup without making changes');
        console.log('  --batch-size=N      - Process N messages per batch (default: 50)');
        console.log('  --verify-migration  - Verify migration before cleanup (default: true)');
        console.log('  --force             - Force cleanup without confirmation');

        console.log('\nStatus Options:');
        console.log('  --detailed          - Show detailed migration status');

        console.log('\nExamples:');
        console.log('  rollback --dry-run                    # Simulate rollback');
        console.log('  rollback --force --delete-source      # Perform rollback with source deletion');
        console.log('  cleanup --dry-run                     # Simulate cleanup');
        console.log('  cleanup --force                       # Perform cleanup');
        console.log('  status --detailed                     # Show detailed status');

        console.log('\n⚠️  IMPORTANT NOTES:');
        console.log('• Always run with --dry-run first to preview changes');
        console.log('• Rollback should only be used in emergency situations');
        console.log('• Cleanup permanently deletes old messages - ensure migration is verified');
        console.log('• Use --force flag for non-interactive execution');

        return { success: true };
    }
}

/**
 * Parse command line arguments
 * @param {Array} args - Command line arguments
 * @returns {Object} Parsed arguments
 */
export const parseArgs = (args) => {
    const parsed = {
        command: args[0] || 'help',
        options: {}
    };

    for (let i = 1; i < args.length; i++) {
        const arg = args[i];

        if (arg.startsWith('--')) {
            const [key, value] = arg.substring(2).split('=');

            if (value !== undefined) {
                // Handle options with values
                if (key === 'batch-size') {
                    parsed.options.batchSize = parseInt(value, 10);
                } else {
                    parsed.options[key] = value;
                }
            } else {
                // Handle boolean flags
                switch (key) {
                    case 'dry-run':
                        parsed.options.dryRun = true;
                        break;
                    case 'delete-source':
                        parsed.options.deleteSource = true;
                        break;
                    case 'verify-integrity':
                        parsed.options.verifyIntegrity = true;
                        break;
                    case 'verify-migration':
                        parsed.options.verifyMigration = true;
                        break;
                    case 'force':
                        parsed.options.force = true;
                        break;
                    case 'detailed':
                        parsed.options.detailed = true;
                        break;
                    default:
                        parsed.options[key] = true;
                }
            }
        }
    }

    return parsed;
};

/**
 * Main CLI execution function
 * @param {Array} args - Command line arguments
 * @param {Object} selectedChat - Chat object
 */
export const runCLI = async (args, selectedChat) => {
    const { command, options } = parseArgs(args);
    const cli = new RollbackCleanupCLI();

    console.log(`\n🚀 Executing: ${command}`);
    if (Object.keys(options).length > 0) {
        console.log(`Options:`, options);
    }

    const result = await cli.execute(command, selectedChat, options);

    if (!result.success) {
        console.error(`\n❌ Command failed: ${result.error}`);
        process.exit(1);
    }

    console.log('\n✅ Command completed successfully');
    return result;
};

// Export CLI instance for direct usage
export const cli = new RollbackCleanupCLI();
