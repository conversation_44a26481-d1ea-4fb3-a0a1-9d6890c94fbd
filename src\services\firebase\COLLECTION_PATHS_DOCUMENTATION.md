# Firebase Collection Paths Documentation

## Overview

This document explains the standardized Firebase collection path structure implemented to ensure consistency between messages and comments across different chat types.

## Background

Previously, the application had inconsistent collection paths:
- **Messages**: Used sender ID as document identifier (`chats/${senderId}/messages`)
- **Comments**: Used chat ID as document identifier (`chats/${chatId}/comments`)

This inconsistency caused data retrieval and synchronization issues. The new standardized approach ensures both messages and comments use the same identifier pattern.

## Standardized Collection Structure

### Chat Types and Identifiers

#### WhatsApp Collections
- **Messages**: `whatsApp/${phoneNumber}/messages`
- **Comments**: `whatsApp/${phoneNumber}/comments`
- **Identifier**: Normalized phone number (without + prefix and spaces)
- **Status**: Already consistent (no migration needed)

#### Messenger/Instagram Collections
- **Messages**: `chats/${backendChatId}/messages` *(changed from senderId)*
- **Comments**: `chats/${backendChatId}/comments` *(no change)*
- **Identifier**: Backend chat ID from the selected chat object
- **Status**: Migrated from sender ID to backend chat ID

## Collection Path Resolution Service

The `collectionPaths.js` service provides centralized logic for determining consistent collection paths:

```javascript
import { getCollectionPaths, getChatIdentifier, determineChatType } from '../../services/firebase/collectionPaths';

// Example usage
const chatType = determineChatType(selectedChat);
const chatId = getChatIdentifier(selectedChat, chatType);
const paths = getCollectionPaths(chatId, chatType);

console.log(paths.messages); // e.g., "chats/12345/messages"
console.log(paths.comments); // e.g., "chats/12345/comments"
```

## Key Functions

### `getChatIdentifier(selectedChat, chatType)`
Extracts the correct chat identifier based on chat type:
- **WhatsApp**: Returns normalized phone number
- **Messenger/Instagram**: Returns backend chat ID

### `getCollectionPaths(chatId, chatType)`
Returns consistent collection paths for both messages and comments:
- Ensures both collections use the same document identifier
- Handles different chat types appropriately

### `determineChatType(selectedChat, activeFilter)`
Automatically determines chat type from chat object properties:
- Checks for WhatsApp indicators (phone number)
- Identifies Instagram flag
- Defaults to Messenger for other cases

## Migration Strategy

### Phase 1: Dual Write (Backward Compatibility)
- Messages written to both old (sender ID) and new (chat ID) paths
- Read operations try new path first, fallback to old path
- Maintains functionality during transition

### Phase 2: Data Migration
- Background process moves existing messages from old to new structure
- Verification ensures data integrity
- Cleanup removes old collection paths after verification

### Phase 3: Single Write (New Structure Only)
- Remove dual write logic
- Use only new consistent paths
- Update all Firebase listeners and queries

## Usage Guidelines

### For New Features
Always use the collection path resolution service:

```javascript
// ✅ Correct approach
const chatType = determineChatType(selectedChat);
const chatId = getChatIdentifier(selectedChat, chatType);
const { messages, comments } = getCollectionPaths(chatId, chatType);

// Use paths for Firebase operations
const messagesRef = collection(db, ...messages.split('/'));
const commentsRef = collection(db, ...comments.split('/'));
```

### For Existing Code Updates
Replace hardcoded paths with service calls:

```javascript
// ❌ Old approach
const messagesPath = `chats/${senderId}/messages`;

// ✅ New approach
const { messages: messagesPath } = getCollectionPaths(chatId, chatType);
```

## Error Handling

The service includes comprehensive error handling:
- Validates input parameters
- Logs warnings for missing data
- Returns null for invalid configurations
- Provides fallback mechanisms during migration

## Performance Considerations

- Collection paths are computed on-demand (no caching needed)
- Chat type determination is optimized for common cases
- Migration utilities include batch processing for large datasets
- Query optimization service uses consistent paths for better performance

## Testing

The collection paths service includes comprehensive tests:
- Unit tests for path resolution logic
- Integration tests for different chat types
- Migration verification tests
- Error scenario handling tests

## Troubleshooting

### Common Issues

1. **Inconsistent Chat IDs**: Ensure `getChatIdentifier()` is used consistently
2. **Migration Errors**: Check migration logs and use verification utilities
3. **Path Resolution Failures**: Validate chat object structure and type

### Debug Logging

Enable debug logging to trace path resolution:

```javascript
console.log('Chat type determination:', {
    selectedChat,
    chatType: determineChatType(selectedChat),
    chatId: getChatIdentifier(selectedChat, chatType)
});
```

## Future Considerations

- New chat types should follow the established pattern
- Collection path changes require migration planning
- Performance monitoring should track query efficiency
- Documentation should be updated for any structural changes

## Related Files

- `src/services/firebase/collectionPaths.js` - Main service implementation
- `src/utils/firebase/dataMigration.js` - Migration utilities
- `src/services/firebase/queryOptimization.js` - Optimized queries
- `src/redux/features/metaBusinessChatSlice.js` - Redux integration
- `src/services/comments/hybridService.js` - Comments integration
