import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import Cookies from 'js-cookie';
import clientService from '../../services/auth/client';
import confirmPaymentApi from '../../services/payment/confirm-payment.api';
import { showSuccessToast, showErrorToast } from '../../utils/toast-success-error';
import { shouldShowPackageFeatures } from '../../config/packageVisibility';

const storedUser = Cookies.get('userData');
const initialUser = storedUser && storedUser !== "undefined" ? JSON.parse(storedUser) : null;
const storedPermissions = Cookies.get('userPermissions');
const initialPermissions = storedPermissions && storedPermissions !== "undefined" ? JSON.parse(storedPermissions) : [];

const initialState = {
  user: initialUser,
  currentUserPermissions: initialPermissions,
  userFlag: 'user',
  showExpiredSessionModal: false,
  packageData: null,
  processing: false
};

export const signUpFlow = createAsyncThunk(
  'auth/signUpFlow',
  async (values, { dispatch, rejectWithValue }) => {
    try {
      const res = await clientService.SignUpApi(values);
      const permissions = res.data.Permissions.map((permission) => permission.name);
      dispatch(setCurrentUserPermissions(permissions));
      dispatch(login({
        token: res.data.token,
        user: res.data.user,
      }));
      setTimeout(() => {
        window.location.href = '/';
      }, 500);
      return { user: { token: res.data.token, user: res.data.user }, permissions };
    } catch (err) {
      return rejectWithValue(err.response?.data?.message || 'Sign up failed');
    }
  }
);

export const handlePurchase = createAsyncThunk(
  'auth/handlePurchase',
  async (_, { getState, dispatch, rejectWithValue }) => {
    const { user, packageData } = getState().auth;

    // Check if user should have access to package features
    const userId = user?.user?.id;
    if (!shouldShowPackageFeatures(userId)) {
      return rejectWithValue('Package features are not available for this user');
    }

    const packageToBuy = packageData || { productname: user?.user?.package_name, packageId: Number(user?.user?.package_id), total: user?.user?.package_price };
    if (!packageToBuy) return;
    try {
      dispatch(setProcessing(true));
      const response = await confirmPaymentApi({
        total: parseInt(packageToBuy.total),
        productname: packageToBuy.productname,
        packageId: Number(packageToBuy.packageId),
      }, userId);
      if (packageToBuy.packageId === 1) {
        dispatch(updateUser(response?.data?.user));
        showSuccessToast('You have successfully purchased the Basic package', { position: 'bottom-right', theme: 'dark' });
        setTimeout(() => {
          window.location.href = '/';
        }, 1000);
      } else if (packageToBuy.packageId === 2) {
        window.location.href = response;
      }
    } catch (error) {
      showErrorToast(error.response?.data?.message || 'An error occurred', { position: 'bottom-right', theme: 'dark' });
      return rejectWithValue(error.response?.data?.message || 'Purchase failed');
    } finally {
      dispatch(setProcessing(false));
    }
  }
);

const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    login: (state, action) => {
      const userData = action.payload;
      Cookies.set('userData', JSON.stringify(userData), { expires: 365 });
      state.user = userData;
    },
    logout: (state) => {
      Cookies.remove('userData');
      Cookies.remove('userPermissions');
      sessionStorage.removeItem('packagePurchase');
      state.user = null;
      state.currentUserPermissions = [];
      window.location.reload();
    },
    setCurrentUserPermissions: (state, action) => {
      Cookies.set('userPermissions', JSON.stringify(action.payload), { expires: 365 });
      state.currentUserPermissions = action.payload;
    },
    setUserFlag: (state, action) => {
      state.userFlag = action.payload;
    },
    setUser: (state, action) => {
      state.user = action.payload;
    },
    setShowExpiredSessionModal: (state, action) => {
      state.showExpiredSessionModal = action.payload;
    },
    setProcessing: (state, action) => {
      state.processing = action.payload;
    },
    setPackageData: (state, action) => {
      // Only set package data if user should see package features
      const userId = state.user?.user?.id;
      if (shouldShowPackageFeatures(userId)) {
        state.packageData = action.payload;
      }
      // For excluded users, packageData remains null/unchanged
    },
    updateUser: (state, action) => {
      if (state.user) {
        state.user.user = { ...state.user.user, ...action.payload };
        Cookies.set('userData', JSON.stringify(state.user));
      }
    }
  },
  extraReducers: (builder) => {
    builder
      .addCase(signUpFlow.fulfilled, (state, action) => {
        state.user = action.payload.user;
        state.currentUserPermissions = action.payload.permissions;
      })
      .addCase(signUpFlow.rejected, (state, action) => {
        // handle error
      })
      .addCase(handlePurchase.pending, (state) => {
        state.processing = true;
      })
      .addCase(handlePurchase.fulfilled, (state) => {
        state.processing = false;
      })
      .addCase(handlePurchase.rejected, (state) => {
        state.processing = false;
      });
  }
});

export const {
  login,
  logout,
  setCurrentUserPermissions,
  setUserFlag,
  setUser,
  setShowExpiredSessionModal,
  setProcessing,
  setPackageData,
  updateUser
} = authSlice.actions;

export default authSlice.reducer;
