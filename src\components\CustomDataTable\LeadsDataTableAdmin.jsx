import { useMemo, useState } from "react";
import {
  useAsyncDebounce,
  useFilters,
  useGlobalFilter,
  usePagination,
  useSortBy,
  useTable,
} from "react-table";
import {
  Button,
  ButtonGroup,
  Col,
  Form,
  InputGroup,
  Pagination,
  Row,
  Table,
} from "react-bootstrap";
import { IoReturnUpBackOutline } from "react-icons/io5";
import { BsFillCaretDownFill } from "react-icons/bs";
import { BiSearch } from "react-icons/bi";
import { ReactSVG } from "react-svg";
import { Link, useParams } from "react-router-dom";
import { useSelector, useDispatch } from "react-redux";
import { handleExportLeadsThunk } from "../../redux/features/clientSlice";
import CreateClientModal from "../Modals/CreateClientModal";
import { FaFilterCircleXmark, FaUserPlus } from "react-icons/fa6";
import { Tooltip } from "react-tooltip";
import CenteredModal from "../Shared/modals/CenteredModal/CenteredModal";
import { getVisibleStatusOptions, isUserExcluded } from "../../config/leadStatusConfig";
import ImportLeadsDropZone from "../Modals/ImportLeadsModal";
import { LuImport } from "react-icons/lu";
import { PiExportBold } from "react-icons/pi";
import FetchingDataAdmin from "../LoadingAnimation/FetchingDataAdmin";
import { useTranslation } from "react-i18next";
import { orderedSourceKeys, sourceToIcon } from "../../constants/sourceIcons";
import AdminPaginationComponent from "../AdminPagination/AdminPaginationComponent";

function GlobalFilter({
  preGlobalFilteredRows,
  globalFilter,
  setGlobalFilter,
}) {
  const { t } = useTranslation();
  const count = preGlobalFilteredRows.length;
  const [value, setValue] = useState(globalFilter);
  const onChange = useAsyncDebounce((value) => {
    setGlobalFilter(value || undefined);
  }, 200);

  return (
    <div className={"position-relative"}>
      <InputGroup>
        <Form.Control
          aria-label="Default"
          aria-describedby="inputGroup-sizing-default"
          value={value || ""}
          onChange={(e) => {
            setValue(e.target.value);
            onChange(e.target.value);
          }}
          placeholder={`${count} records...`}
          className={"search-input"}
        />
      </InputGroup>
      <div className={"search-icon"}>
        <BiSearch color={"#000"} size={20} />
      </div>
    </div>
  );
}

function DefaultColumnFilter({
  column: { filterValue, preFilteredRows, setFilter },
}) {
  const count = preFilteredRows.length;

  return (
    <input
      className="form-control"
      value={filterValue || ""}
      onChange={(e) => {
        setFilter(e.target.value || undefined);
      }}
      placeholder={`Search ${count} records...`}
    />
  );
}

const LeadsDataTableAdmin = ({
  columns,
  data,
  handleFilterStatus,
  leadStatusCounts,
  loading,
  classNames,
  setCurrentPage,
  currentPage,
  recordsPerPage,
  setRecordsPerPage,
  totalPages,
}) => {
  const { t } = useTranslation();
  const { completed, inprogress, pendding, rejected } = leadStatusCounts;
  const all =
    parseInt(completed) +
    parseInt(inprogress) +
    parseInt(pendding) +
    parseInt(rejected);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const handleClose = () => setShowCreateModal(false);
  const handleShow = () => setShowCreateModal(true);
  const dispatch = useDispatch();
  const { leads } = useSelector((state) => state.client);
  const handleExportLeads = (clientId) =>
    dispatch(handleExportLeadsThunk(clientId));
  const [selectedSource, setSelectedSource] = useState(null);
  const [showImportLeadModal, setShowImportLeadModal] = useState(false);
  const defaultColumn = useMemo(
    () => ({
      // Default Filter UI
      Filter: DefaultColumnFilter,
    }),
    []
  );
  const {
    getTableProps,
    getTableBodyProps,
    headerGroups,
    prepareRow,
    preGlobalFilteredRows,
    setGlobalFilter,
    setFilter,
    canPreviousPage,
    canNextPage,
    gotoPage,
    setPageSize,
    rows,
    state: { pageIndex, pageSize, globalFilter },
  } = useTable(
    {
      columns,
      data,
      defaultColumn,
      initialState: { pageIndex: currentPage - 1, pageSize: recordsPerPage },
    },
    useFilters,
    useGlobalFilter,
    useSortBy,
    usePagination
  );

  const handleSourceIconFilterClick = (source) => {
    setSelectedSource(source);
    setFilter("source", source);
  };

  const handleClearFilters = () => {
    setGlobalFilter("");
    setSelectedSource(null);
    columns.forEach((column) => {
      if (column.accessor) {
        setFilter(column.accessor, undefined);
      }
    });
  };

  // Get current user from Redux store
  const { user } = useSelector((state) => state.auth);
  const userId = user?.user?.id;
  
  // Get visible status options based on user role
  const visibleStatusOptions = getVisibleStatusOptions(userId);
  
  // Create status options array for the filter buttons
  const statusOptions = [
    { key: "all", label: "All", value: "all", count: all },
    // Only show status options that should be visible to this user
    ...Object.entries(visibleStatusOptions).map(([value, label]) => {
      // Create a key based on the label
      const key = label.toLowerCase().replace(/\s+/g, '');
      
      // Determine the count based on the status value
      let count = 0;
      switch (parseInt(value)) {
        case 0: count = parseInt(pendding); break;
        case 1: count = parseInt(inprogress); break;
        case 2: count = parseInt(completed); break;
        case 3: count = parseInt(rejected); break;
        // Add more cases for other status counts as needed
        default: count = 0;
      }
      
      return {
        key,
        label,
        value: parseInt(value),
        count
      };
    })
  ];
  const handlePageChange = (pageIndex) => {
    setCurrentPage(pageIndex + 1);
    gotoPage(pageIndex);
  };

  const handlePageSizeChange = (size) => {
    setRecordsPerPage(size);
    setPageSize(size);
  };
  const params = useParams();
  return loading ? (
    <FetchingDataAdmin className={"admin-theme"} />
  ) : (
    <>
      <CenteredModal
        size={"lg"}
        show={showImportLeadModal}
        children={
          <ImportLeadsDropZone
            handleClose={() => setShowImportLeadModal(false)}
          />
        }
        onHide={() => setShowImportLeadModal(false)}
      />
      <div className={`all-leads-table ${classNames}`}>
        <Row className={"m-4"}>
          <Row>
            {leads?.length !== 0 ? (
              <>
                <Col lg={6}>
                  <ButtonGroup className="mb-2 flex-wrap">
                    {statusOptions.map((status) => (
                      <Button
                        key={status.key}
                        variant="link"
                        className={`custom-button ${data.status === status.value ? "active" : ""
                          }`}
                        onClick={() => handleFilterStatus(status.value)}
                      >
                        {status.label} {status.count}
                      </Button>
                    ))}
                  </ButtonGroup>
                </Col>
                <Col lg={6}>
                  <GlobalFilter
                    preGlobalFilteredRows={preGlobalFilteredRows}
                    globalFilter={globalFilter}
                    setGlobalFilter={setGlobalFilter}
                  />
                </Col>
              </>
            ) : null}
          </Row>

          <Row
            className={`mt-3 align-items-center ${leads?.length !== 0
                ? "justify-content-between"
                : "justify-content-center"
              }`}
          >
            {leads?.length !== 0 ? (
              <Col lg={6} className="social-filter-wrapper my-3">
                <div className="social-filter-container">
                  <div
                    onClick={handleClearFilters}
                    className={`reset${selectedSource === null ? " reset-selected" : ""
                      }`}
                  >
                    {selectedSource === null ? (
                      <div
                        className={
                          "d-flex justify-content-between align-items-center"
                        }
                      >
                        <div className={"me-2"}>
                          <IoReturnUpBackOutline size={20} />
                        </div>
                        <div>{t("common.backToAll")}</div>
                      </div>
                    ) : (
                      <div
                        className={
                          "d-flex justify-content-between align-items-center"
                        }
                      >
                        <div className={"me-2"}>
                          <IoReturnUpBackOutline size={20} />
                        </div>
                        <div>Back To All</div>
                      </div>
                    )}
                  </div>
                  {orderedSourceKeys.map((source, index) => (
                    <div
                      key={index}
                      className={`social-icon${selectedSource === source ? " selected" : ""
                        }`}
                      onClick={() => handleSourceIconFilterClick(source)}
                    >
                      <ReactSVG src={sourceToIcon[source]} />
                    </div>
                  ))}
                </div>
              </Col>
            ) : null}

            <Col
              className={
                "d-flex justify-content-center justify-content-lg-end flex-column flex-md-row align-items-center"
              }
              lg={6}
            >
              <div
                className={"clear-filter text-white"}
                onClick={handleClearFilters}
              >
                <FaFilterCircleXmark size={25} />
              </div>
              <span
                className={"import-leads"}
                onClick={() => setShowImportLeadModal(true)}
              >
                <LuImport size={25} />
              </span>
              <span
                className={"export-leads text-white"}
                onClick={() => handleExportLeads(params?.id)}
              >
                <PiExportBold size={25} />
              </span>
              <div className={"add-lead"} onClick={handleShow}>
                <FaUserPlus size={25} />
              </div>
              <Tooltip
                anchorSelect=".clear-filter"
                content="Clear search and filters."
              />
              <Tooltip anchorSelect=".add-lead" content="Add a new lead." />
              <Tooltip
                anchorSelect=".import-leads"
                content="Import leads from csv, xsxl file."
              />
              <Tooltip
                anchorSelect=".export-leads"
                content="Export leads to csv, xsxl file."
              />
              <CreateClientModal
                show={showCreateModal}
                onHide={handleClose}
                className={"admin-theme"}
              />
            </Col>
          </Row>
        </Row>
        <Table
          responsive={"md"}
          className="table text-center position-relative"
          {...getTableProps()}
        >
          {loading ? (
            <FetchingDataAdmin className={"admin-theme"} />
          ) : (
            <>
              <thead>
                {headerGroups.map((headerGroup, index) => (
                  <tr {...headerGroup.getHeaderGroupProps()} key={index}>
                    {headerGroup.headers.map((column, j) => (
                      <th
                        {...column.getHeaderProps(
                          column.getSortByToggleProps()
                        )}
                        key={j}
                      >
                        {column.render("Header")}
                        {/* Render the columns filter UI */}
                        {/*<div>{column.canFilter ? column.render('Filter') : null}</div>*/}
                        <span>
                          {column.isSorted ? (
                            column.isSortedDesc ? (
                              " 🔽"
                            ) : (
                              " 🔼"
                            )
                          ) : (
                            <> {column.accessor && <BsFillCaretDownFill />}</>
                          )}
                        </span>
                      </th>
                    ))}
                  </tr>
                ))}
              </thead>
              <tbody {...getTableBodyProps()}>
                {rows.map((row) => {
                  prepareRow(row);
                  return (
                    <tr
                      {...row.getRowProps()}
                      className={"client-table-row filter-table-rows"}
                      key={row.original.id}
                    >
                      {row.cells.map((cell, j) => {
                        return (
                          <td {...cell.getCellProps()} key={j}>
                            {cell.column.id === "Actions" ||
                              cell.column.id === "lastActivity" ||
                              cell.column.id === "assignedTo" ? (
                              cell.render("Cell")
                            ) : (
                              <Link
                                to={`/admin/clients/leads/profile/${row.original.id}`}
                                className={"p-2"}
                              >
                                {cell.render("Cell")}
                              </Link>
                            )}
                          </td>
                        );
                      })}
                    </tr>
                  );
                })}
              </tbody>
            </>
          )}
        </Table>
        <AdminPaginationComponent
          currentPage={currentPage}
          totalPages={totalPages}
          itemsPerPage={recordsPerPage}
          onPageChange={(page) => {
            setCurrentPage(page);
          }}
          onItemsPerPageChange={(size) => {
            setRecordsPerPage(size);
            setCurrentPage(1);
          }}
          itemsPerPageOptions={[10, 20, 30, 40, 50]}
        />
      </div>
    </>
  );
};

export default LeadsDataTableAdmin;
