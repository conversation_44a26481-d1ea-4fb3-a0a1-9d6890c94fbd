/**
 * Firebase Migration Monitoring and Logging Service
 *
 * This module provides comprehensive monitoring and logging capabilities for
 * the Firebase collection migration process from sender ID to chat ID structure.
 */

import {
    collection,
    doc,
    setDoc,
    getDoc,
    updateDoc,
    query,
    where,
    getDocs,
    orderBy,
    serverTimestamp
} from 'firebase/firestore';
import { db } from '../firebase.config';

/**
 * Migration log levels
 */
export const LOG_LEVELS = {
    INFO: 'info',
    WARN: 'warn',
    ERROR: 'error',
    SUCCESS: 'success'
};

/**
 * Migration status types
 */
export const MIGRATION_STATUS = {
    NOT_STARTED: 'not_started',
    IN_PROGRESS: 'in_progress',
    COMPLETED: 'completed',
    FAILED: 'failed',
    ROLLED_BACK: 'rolled_back'
};

/**
 * Migration operation types
 */
export const OPERATION_TYPES = {
    MIGRATION: 'migration',
    ROLLBACK: 'rollback',
    VERIFICATION: 'verification',
    CLEANUP: 'cleanup'
};

/**
 * Log a migration operation with detailed metadata
 * @param {string} operation - The operation type (from OPERATION_TYPES)
 * @param {string} level - The log level (from LOG_LEVELS)
 * @param {string} message - The log message
 * @param {Object} metadata - Additional metadata
 * @param {string} chatId - The chat identifier
 * @param {string} chatType - The chat type
 */
export const logMigrationOperation = async (operation, level, message, metadata = {}, chatId = null, chatType = null) => {
    try {
        const logEntry = {
            timestamp: new Date().toISOString(),
            operation,
            level,
            message,
            chatId,
            chatType,
            metadata: {
                ...metadata,
                userAgent: typeof navigator !== 'undefined' ? navigator.userAgent : 'server',
                sessionId: getSessionId()
            }
        };

        // Log to console with appropriate level
        const consoleMessage = `[MIGRATION_${level.toUpperCase()}] ${operation}: ${message}`;
        switch (level) {
            case LOG_LEVELS.ERROR:
                console.error(consoleMessage, logEntry);
                break;
            case LOG_LEVELS.WARN:
                console.warn(consoleMessage, logEntry);
                break;
            case LOG_LEVELS.SUCCESS:
                console.log(`✅ ${consoleMessage}`, logEntry);
                break;
            default:
                console.log(consoleMessage, logEntry);
        }

        // Store in Firebase for persistence and analysis
        await storeMigrationLog(logEntry);

        // Update migration status if this is a status-changing operation
        if (chatId && chatType && shouldUpdateStatus(operation, level)) {
            await updateMigrationStatus(chatId, chatType, operation, level, metadata);
        }

    } catch (error) {
        console.error('Error logging migration operation:', error);
        // Don't throw - logging failures shouldn't break the migration
    }
};

/**
 * Store migration log entry in Firebase
 * @private
 */
const storeMigrationLog = async (logEntry) => {
    try {
        const logId = `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        const logRef = doc(db, 'migration_logs', logId);
        await setDoc(logRef, {
            ...logEntry,
            createdAt: serverTimestamp()
        });
    } catch (error) {
        console.error('Error storing migration log:', error);
        // Don't throw - this is not critical
    }
};

/**
 * Update migration status for a specific chat
 * @param {string} chatId - The chat identifier
 * @param {string} chatType - The chat type
 * @param {string} operation - The operation type
 * @param {string} level - The log level
 * @param {Object} metadata - Additional metadata
 */
export const updateMigrationStatus = async (chatId, chatType, operation, level, metadata = {}) => {
    try {
        const statusId = `${chatType}_${chatId}`;
        const statusRef = doc(db, 'migration_status', statusId);

        // Get current status
        const currentStatus = await getDoc(statusRef);
        const existingData = currentStatus.exists() ? currentStatus.data() : {};

        // Determine new status based on operation and level
        let newStatus = existingData.status || MIGRATION_STATUS.NOT_STARTED;

        if (operation === OPERATION_TYPES.MIGRATION) {
            if (level === LOG_LEVELS.INFO && metadata.phase === 'start') {
                newStatus = MIGRATION_STATUS.IN_PROGRESS;
            } else if (level === LOG_LEVELS.SUCCESS) {
                newStatus = MIGRATION_STATUS.COMPLETED;
            } else if (level === LOG_LEVELS.ERROR) {
                newStatus = MIGRATION_STATUS.FAILED;
            }
        } else if (operation === OPERATION_TYPES.ROLLBACK && level === LOG_LEVELS.SUCCESS) {
            newStatus = MIGRATION_STATUS.ROLLED_BACK;
        }

        const statusData = {
            chatId,
            chatType,
            status: newStatus,
            lastOperation: operation,
            lastOperationLevel: level,
            lastUpdated: new Date().toISOString(),
            updatedAt: serverTimestamp(),
            ...existingData,
            // Merge metadata
            metadata: {
                ...existingData.metadata,
                ...metadata
            }
        };

        // Add creation timestamp if this is a new status document
        if (!currentStatus.exists()) {
            statusData.createdAt = serverTimestamp();
            statusData.firstOperation = operation;
        }

        await setDoc(statusRef, statusData, { merge: true });

        console.log(`Updated migration status for ${chatType} chat ${chatId}: ${newStatus}`);

    } catch (error) {
        console.error('Error updating migration status:', error);
        // Don't throw - this is not critical for the migration process
    }
};

/**
 * Get migration status for a specific chat
 * @param {string} chatId - The chat identifier
 * @param {string} chatType - The chat type
 * @returns {Promise<Object|null>} - Migration status or null if not found
 */
export const getMigrationStatus = async (chatId, chatType) => {
    try {
        const statusId = `${chatType}_${chatId}`;
        const statusRef = doc(db, 'migration_status', statusId);
        const statusDoc = await getDoc(statusRef);

        if (statusDoc.exists()) {
            const data = statusDoc.data();
            return {
                ...data,
                lastUpdated: data.lastUpdated || data.updatedAt?.toDate?.()?.toISOString(),
                createdAt: data.createdAt?.toDate?.()?.toISOString()
            };
        }

        return null;
    } catch (error) {
        console.error('Error getting migration status:', error);
        return null;
    }
};

/**
 * Get migration statistics across all chats
 * @returns {Promise<Object>} - Migration statistics
 */
export const getMigrationStatistics = async () => {
    try {
        const statusCollection = collection(db, 'migration_status');
        const statusSnapshot = await getDocs(statusCollection);

        const stats = {
            total: 0,
            notStarted: 0,
            inProgress: 0,
            completed: 0,
            failed: 0,
            rolledBack: 0,
            byType: {
                whatsapp: { total: 0, completed: 0, failed: 0 },
                messenger: { total: 0, completed: 0, failed: 0 },
                instagram: { total: 0, completed: 0, failed: 0 }
            }
        };

        statusSnapshot.docs.forEach(doc => {
            const data = doc.data();
            stats.total++;

            // Count by status
            switch (data.status) {
                case MIGRATION_STATUS.NOT_STARTED:
                    stats.notStarted++;
                    break;
                case MIGRATION_STATUS.IN_PROGRESS:
                    stats.inProgress++;
                    break;
                case MIGRATION_STATUS.COMPLETED:
                    stats.completed++;
                    break;
                case MIGRATION_STATUS.FAILED:
                    stats.failed++;
                    break;
                case MIGRATION_STATUS.ROLLED_BACK:
                    stats.rolledBack++;
                    break;
            }

            // Count by chat type
            if (data.chatType && stats.byType[data.chatType]) {
                stats.byType[data.chatType].total++;
                if (data.status === MIGRATION_STATUS.COMPLETED) {
                    stats.byType[data.chatType].completed++;
                } else if (data.status === MIGRATION_STATUS.FAILED) {
                    stats.byType[data.chatType].failed++;
                }
            }
        });

        return stats;
    } catch (error) {
        console.error('Error getting migration statistics:', error);
        return {
            total: 0,
            error: error.message
        };
    }
};

/**
 * Get recent migration logs
 * @param {number} limit - Maximum number of logs to retrieve
 * @param {string} level - Filter by log level (optional)
 * @param {string} operation - Filter by operation type (optional)
 * @returns {Promise<Array>} - Array of log entries
 */
export const getRecentMigrationLogs = async (limit = 100, level = null, operation = null) => {
    try {
        let logsQuery = query(
            collection(db, 'migration_logs'),
            orderBy('timestamp', 'desc')
        );

        // Add filters if specified
        if (level) {
            logsQuery = query(logsQuery, where('level', '==', level));
        }

        if (operation) {
            logsQuery = query(logsQuery, where('operation', '==', operation));
        }

        const logsSnapshot = await getDocs(logsQuery);
        const logs = [];

        logsSnapshot.docs.slice(0, limit).forEach(doc => {
            const data = doc.data();
            logs.push({
                id: doc.id,
                ...data,
                createdAt: data.createdAt?.toDate?.()?.toISOString() || data.timestamp
            });
        });

        return logs;
    } catch (error) {
        console.error('Error getting recent migration logs:', error);
        return [];
    }
};

/**
 * Monitor migration progress for a specific chat
 * @param {string} chatId - The chat identifier
 * @param {string} chatType - The chat type
 * @param {Function} onProgress - Callback function for progress updates
 * @returns {Function} - Unsubscribe function
 */
export const monitorMigrationProgress = (chatId, chatType, onProgress) => {
    let isMonitoring = true;

    const checkProgress = async () => {
        if (!isMonitoring) return;

        try {
            const status = await getMigrationStatus(chatId, chatType);
            onProgress(status);

            // Continue monitoring if migration is in progress
            if (status && status.status === MIGRATION_STATUS.IN_PROGRESS) {
                setTimeout(checkProgress, 2000); // Check every 2 seconds
            }
        } catch (error) {
            console.error('Error monitoring migration progress:', error);
            onProgress({ error: error.message });
        }
    };

    // Start monitoring
    checkProgress();

    // Return unsubscribe function
    return () => {
        isMonitoring = false;
    };
};

/**
 * Create a performance monitoring wrapper for migration operations
 * @param {string} operationName - Name of the operation
 * @param {Function} operation - The operation function to wrap
 * @param {Object} context - Context information (chatId, chatType, etc.)
 * @returns {Function} - Wrapped operation function
 */
export const withPerformanceMonitoring = (operationName, operation, context = {}) => {
    return async (...args) => {
        const startTime = Date.now();
        const { chatId, chatType } = context;

        await logMigrationOperation(
            OPERATION_TYPES.MIGRATION,
            LOG_LEVELS.INFO,
            `Starting ${operationName}`,
            { phase: 'start', operationName },
            chatId,
            chatType
        );

        try {
            const result = await operation(...args);
            const duration = Date.now() - startTime;

            await logMigrationOperation(
                OPERATION_TYPES.MIGRATION,
                LOG_LEVELS.SUCCESS,
                `Completed ${operationName}`,
                {
                    phase: 'complete',
                    operationName,
                    duration,
                    result: typeof result === 'object' ? JSON.stringify(result) : result
                },
                chatId,
                chatType
            );

            return result;
        } catch (error) {
            const duration = Date.now() - startTime;

            await logMigrationOperation(
                OPERATION_TYPES.MIGRATION,
                LOG_LEVELS.ERROR,
                `Failed ${operationName}: ${error.message}`,
                {
                    phase: 'error',
                    operationName,
                    duration,
                    error: error.message,
                    stack: error.stack
                },
                chatId,
                chatType
            );

            throw error;
        }
    };
};

/**
 * Generate a migration report for a specific time period
 * @param {Date} startDate - Start date for the report
 * @param {Date} endDate - End date for the report
 * @returns {Promise<Object>} - Migration report
 */
export const generateMigrationReport = async (startDate, endDate) => {
    try {
        const report = {
            period: {
                start: startDate.toISOString(),
                end: endDate.toISOString()
            },
            summary: {
                totalOperations: 0,
                successfulOperations: 0,
                failedOperations: 0,
                averageDuration: 0
            },
            operationBreakdown: {},
            chatTypeBreakdown: {},
            errors: []
        };

        // Get logs for the specified period
        const logsQuery = query(
            collection(db, 'migration_logs'),
            where('timestamp', '>=', startDate.toISOString()),
            where('timestamp', '<=', endDate.toISOString()),
            orderBy('timestamp', 'desc')
        );

        const logsSnapshot = await getDocs(logsQuery);
        const durations = [];

        logsSnapshot.docs.forEach(doc => {
            const data = doc.data();
            report.summary.totalOperations++;

            // Count by level
            if (data.level === LOG_LEVELS.SUCCESS) {
                report.summary.successfulOperations++;
            } else if (data.level === LOG_LEVELS.ERROR) {
                report.summary.failedOperations++;
                report.errors.push({
                    timestamp: data.timestamp,
                    message: data.message,
                    chatId: data.chatId,
                    chatType: data.chatType,
                    operation: data.operation
                });
            }

            // Count by operation type
            if (!report.operationBreakdown[data.operation]) {
                report.operationBreakdown[data.operation] = { total: 0, success: 0, failed: 0 };
            }
            report.operationBreakdown[data.operation].total++;
            if (data.level === LOG_LEVELS.SUCCESS) {
                report.operationBreakdown[data.operation].success++;
            } else if (data.level === LOG_LEVELS.ERROR) {
                report.operationBreakdown[data.operation].failed++;
            }

            // Count by chat type
            if (data.chatType) {
                if (!report.chatTypeBreakdown[data.chatType]) {
                    report.chatTypeBreakdown[data.chatType] = { total: 0, success: 0, failed: 0 };
                }
                report.chatTypeBreakdown[data.chatType].total++;
                if (data.level === LOG_LEVELS.SUCCESS) {
                    report.chatTypeBreakdown[data.chatType].success++;
                } else if (data.level === LOG_LEVELS.ERROR) {
                    report.chatTypeBreakdown[data.chatType].failed++;
                }
            }

            // Collect durations for average calculation
            if (data.metadata && data.metadata.duration) {
                durations.push(data.metadata.duration);
            }
        });

        // Calculate average duration
        if (durations.length > 0) {
            report.summary.averageDuration = durations.reduce((sum, duration) => sum + duration, 0) / durations.length;
        }

        return report;
    } catch (error) {
        console.error('Error generating migration report:', error);
        return {
            error: error.message
        };
    }
};

/**
 * Helper functions
 */

/**
 * Get or create a session ID for tracking related operations
 * @private
 */
const getSessionId = () => {
    if (typeof window !== 'undefined') {
        let sessionId = sessionStorage.getItem('migration_session_id');
        if (!sessionId) {
            sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
            sessionStorage.setItem('migration_session_id', sessionId);
        }
        return sessionId;
    }
    return `server_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
};

/**
 * Determine if an operation should update the migration status
 * @private
 */
const shouldUpdateStatus = (operation, level) => {
    return (
        operation === OPERATION_TYPES.MIGRATION ||
        operation === OPERATION_TYPES.ROLLBACK ||
        (operation === OPERATION_TYPES.VERIFICATION && level === LOG_LEVELS.ERROR)
    );
};

/**
 * Clean up old migration logs (older than specified days)
 * @param {number} daysToKeep - Number of days to keep logs
 * @returns {Promise<number>} - Number of logs deleted
 */
export const cleanupOldMigrationLogs = async (daysToKeep = 30) => {
    try {
        const cutoffDate = new Date();
        cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);

        const oldLogsQuery = query(
            collection(db, 'migration_logs'),
            where('timestamp', '<', cutoffDate.toISOString())
        );

        const oldLogsSnapshot = await getDocs(oldLogsQuery);
        const deletePromises = oldLogsSnapshot.docs.map(doc => doc.ref.delete());

        await Promise.all(deletePromises);

        console.log(`Cleaned up ${deletePromises.length} old migration logs`);
        return deletePromises.length;
    } catch (error) {
        console.error('Error cleaning up old migration logs:', error);
        return 0;
    }
};
