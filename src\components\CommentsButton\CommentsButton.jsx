import React from "react";
import { useSelector, useDispatch } from "react-redux";
import { FaComment } from "react-icons/fa";
import {
  selectCommentsUnreadCount,
  selectCommentsModalOpen,
  openCommentsModal,
} from "../../redux/features/metaBusinessChatSlice";
import "./CommentsButton.css";

const CommentsButton = ({ disabled = false, onClick }) => {
  const dispatch = useDispatch();
  const unreadCount = useSelector(selectCommentsUnreadCount);
  const isModalOpen = useSelector(selectCommentsModalOpen);

  console.log('💬 [COMMENTS_BUTTON] Render state:', {
    unreadCount,
    isModalOpen,
    disabled
  });

  const handleClick = () => {
    if (onClick) {
      onClick();
    } else {
      dispatch(openCommentsModal());
    }
  };

  return (
    <button
      type="button"
      className={`comments-button ${unreadCount > 0 ? "has-unread" : ""} ${
        isModalOpen ? "active" : ""
      }`}
      onClick={handleClick}
      disabled={disabled}
      title={`Comments ${unreadCount > 0 ? `(${unreadCount} unread)` : ""}`}
    >
      <FaComment className="comments-icon" size={18} />
      {unreadCount > 0 && (
        <span className="notification-badge">
          {unreadCount > 99 ? "99+" : unreadCount}
        </span>
      )}
    </button>
  );
};

export default CommentsButton;
