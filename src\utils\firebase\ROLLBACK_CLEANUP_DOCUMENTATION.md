# Firebase Collection Rollback and Cleanup Documentation

## Overview

This document provides comprehensive guidance for rolling back Firebase collection migrations and cleaning up old collection paths. These procedures are critical for maintaining data integrity and managing the transition between sender ID-based and chat ID-based collection structures.

## Table of Contents

1. [Emergency Rollback Procedures](#emergency-rollback-procedures)
2. [Cleanup Procedures](#cleanup-procedures)
3. [Data Integrity Verification](#data-integrity-verification)
4. [CLI Usage Guide](#cli-usage-guide)
5. [Troubleshooting](#troubleshooting)
6. [Best Practices](#best-practices)

## Emergency Rollback Procedures

### When to Use Rollback

Rollback should only be used in emergency situations when:

- Critical issues are discovered with the new chat ID-based structure
- Data corruption is detected in the migrated collections
- Application functionality is severely impacted by the migration
- Immediate reversion to the previous structure is required

### Pre-Rollback Checklist

Before performing a rollback:

1. **Assess the Situation**
   - Document the specific issues requiring rollback
   - Determine the scope of affected chats
   - Estimate the impact of rollback on users

2. **Backup Current State**
   - Export current chat ID-based collections
   - Document current system state
   - Prepare rollback plan with timeline

3. **Verify Rollback Feasibility**
   - Ensure sender ID-based structure can accommodate current data
   - Check for any data that might be lost during rollback
   - Verify application compatibility with old structure

### Rollback Execution Steps

#### Step 1: Preparation

```javascript
import { rollbackToChatIdStructure, getComprehensiveStatus } from './rollbackCleanup';

// Check current status
const status = await getComprehensiveStatus(selectedChat);
console.log('Current status:', status);

// Verify rollback is possible
if (!status.canRollback) {
    throw new Error('Rollback not possible - no messages in chat ID structure');
}
```

#### Step 2: Dry Run Rollback

```javascript
// Always perform dry run first
const dryRunResult = await rollbackToChatIdStructure(selectedChat, {
    dryRun: true,
    verifyIntegrity: true
});

console.log('Dry run results:', dryRunResult);

// Review results before proceeding
if (!dryRunResult.success) {
    console.error('Dry run failed:', dryRunResult.errors);
    // Address issues before proceeding
}
```

#### Step 3: Execute Rollback

```javascript
// Perform actual rollback
const rollbackResult = await rollbackToChatIdStructure(selectedChat, {
    dryRun: false,
    deleteSource: false, // Keep chat ID messages initially
    batchSize: 50,
    verifyIntegrity: true
});

console.log('Rollback results:', rollbackResult);

if (rollbackResult.success) {
    console.log(`Successfully rolled back ${rollbackResult.rolledBackCount} messages`);
} else {
    console.error('Rollback failed:', rollbackResult.errors);
    // Implement recovery procedures
}
```

#### Step 4: Verification and Cleanup

```javascript
// Verify rollback success
const postRollbackStatus = await getComprehensiveStatus(selectedChat);
console.log('Post-rollback status:', postRollbackStatus);

// If rollback successful and verified, optionally clean up chat ID messages
if (rollbackResult.success && postRollbackStatus.status === 'not_migrated') {
    const cleanupResult = await rollbackToChatIdStructure(selectedChat, {
        dryRun: false,
        deleteSource: true, // Now delete chat ID messages
        batchSize: 50
    });
    console.log('Cleanup results:', cleanupResult);
}
```

### CLI Rollback Commands

```bash
# Check status before rollback
node rollbackCleanupCLI.js status --detailed

# Perform dry run rollback
node rollbackCleanupCLI.js rollback --dry-run

# Execute rollback (emergency only)
node rollbackCleanupCLI.js rollback --force --verify-integrity

# Execute rollback with source deletion
node rollbackCleanupCLI.js rollback --force --delete-source
```

## Cleanup Procedures

### When to Perform Cleanup

Cleanup should be performed when:

- Migration has been completed successfully
- Data integrity has been verified
- System has been stable with new structure for sufficient time
- Old sender ID-based collections are no longer needed

### Pre-Cleanup Checklist

Before performing cleanup:

1. **Verify Migration Completion**
   - All messages have been successfully migrated
   - No data integrity issues detected
   - System functioning correctly with new structure

2. **Backup Old Collections**
   - Export sender ID-based collections as backup
   - Document collection paths being cleaned
   - Prepare recovery plan if needed

3. **System Stability Check**
   - Monitor system performance with new structure
   - Verify no critical issues in production
   - Confirm user experience is satisfactory

### Cleanup Execution Steps

#### Step 1: Pre-Cleanup Verification

```javascript
import { cleanupOldSenderIdPaths, getComprehensiveStatus } from './rollbackCleanup';
import { verifyMigrationIntegrity } from './dataMigration';

// Check comprehensive status
const status = await getComprehensiveStatus(selectedChat);
console.log('Current status:', status);

// Verify migration integrity
const integrityResult = await verifyMigrationIntegrity(selectedChat);
console.log('Migration integrity:', integrityResult);

// Ensure cleanup is recommended
if (status.recommendedAction !== 'cleanup') {
    console.warn(`Status recommends: ${status.recommendedAction}, not cleanup`);
    // Proceed only if explicitly intended
}
```

#### Step 2: Dry Run Cleanup

```javascript
// Always perform dry run first
const dryRunResult = await cleanupOldSenderIdPaths(selectedChat, {
    dryRun: true,
    verifyMigration: true
});

console.log('Cleanup dry run results:', dryRunResult);

// Review what would be deleted
console.log(`Would delete ${dryRunResult.deletedCount} messages from ${dryRunResult.cleanedPath}`);
```

#### Step 3: Execute Cleanup

```javascript
// Perform actual cleanup
const cleanupResult = await cleanupOldSenderIdPaths(selectedChat, {
    dryRun: false,
    batchSize: 50,
    verifyMigration: true
});

console.log('Cleanup results:', cleanupResult);

if (cleanupResult.success) {
    console.log(`Successfully deleted ${cleanupResult.deletedCount} old messages`);
} else {
    console.error('Cleanup failed:', cleanupResult.errors);
    // Implement recovery procedures
}
```

#### Step 4: Post-Cleanup Verification

```javascript
// Verify cleanup success
const postCleanupStatus = await getComprehensiveStatus(selectedChat);
console.log('Post-cleanup status:', postCleanupStatus);

// Should show 'fully_migrated_and_cleaned'
if (postCleanupStatus.status === 'fully_migrated_and_cleaned') {
    console.log('✅ Cleanup completed successfully');
} else {
    console.warn('⚠️ Unexpected status after cleanup:', postCleanupStatus.status);
}
```

### CLI Cleanup Commands

```bash
# Check status before cleanup
node rollbackCleanupCLI.js status --detailed

# Perform dry run cleanup
node rollbackCleanupCLI.js cleanup --dry-run

# Execute cleanup
node rollbackCleanupCLI.js cleanup --force --verify-migration

# Check status after cleanup
node rollbackCleanupCLI.js status
```

## Data Integrity Verification

### Verification Process

Data integrity verification ensures that:

- All messages exist in the target collection
- Message data is identical between source and destination
- No data corruption occurred during operations
- Collection structure is consistent

### Manual Verification Steps

```javascript
import { verifyMigrationIntegrity } from './dataMigration';

// Perform comprehensive verification
const verificationResult = await verifyMigrationIntegrity(selectedChat);

console.log('Verification Results:');
console.log(`Success: ${verificationResult.success}`);
console.log(`Source Count: ${verificationResult.sourceCount}`);
console.log(`Destination Count: ${verificationResult.destinationCount}`);
console.log(`Missing in Destination: ${verificationResult.missingInDestination.length}`);
console.log(`Extra in Destination: ${verificationResult.extraInDestination.length}`);
console.log(`Data Integrity Issues: ${verificationResult.dataIntegrityIssues.length}`);

// Handle verification failures
if (!verificationResult.success) {
    console.error('Verification failed:');

    if (verificationResult.missingInDestination.length > 0) {
        console.error('Missing messages:', verificationResult.missingInDestination);
    }

    if (verificationResult.dataIntegrityIssues.length > 0) {
        console.error('Data integrity issues:', verificationResult.dataIntegrityIssues);
    }
}
```

## CLI Usage Guide

### Installation and Setup

```bash
# Navigate to the Firebase utilities directory
cd src/utils/firebase

# The CLI is ready to use with Node.js
node rollbackCleanupCLI.js help
```

### Common Commands

#### Status Check
```bash
# Basic status
node rollbackCleanupCLI.js status

# Detailed status with migration info
node rollbackCleanupCLI.js status --detailed
```

#### Rollback Operations
```bash
# Dry run rollback (safe to test)
node rollbackCleanupCLI.js rollback --dry-run

# Emergency rollback with integrity verification
node rollbackCleanupCLI.js rollback --force --verify-integrity

# Rollback with source deletion (removes chat ID messages)
node rollbackCleanupCLI.js rollback --force --delete-source

# Rollback with custom batch size
node rollbackCleanupCLI.js rollback --force --batch-size=100
```

#### Cleanup Operations
```bash
# Dry run cleanup (safe to test)
node rollbackCleanupCLI.js cleanup --dry-run

# Standard cleanup with migration verification
node rollbackCleanupCLI.js cleanup --force --verify-migration

# Cleanup with custom batch size
node rollbackCleanupCLI.js cleanup --force --batch-size=100

# Cleanup without migration verification (not recommended)
node rollbackCleanupCLI.js cleanup --force --verify-migration=false
```

### CLI Options Reference

| Option               | Description                           | Default | Commands          |
| -------------------- | ------------------------------------- | ------- | ----------------- |
| `--dry-run`          | Simulate operation without changes    | false   | rollback, cleanup |
| `--force`            | Execute without confirmation prompts  | false   | rollback, cleanup |
| `--delete-source`    | Delete source messages after rollback | false   | rollback          |
| `--verify-integrity` | Verify data integrity before rollback | true    | rollback          |
| `--verify-migration` | Verify migration before cleanup       | true    | cleanup           |
| `--batch-size=N`     | Number of messages per batch          | 50      | rollback, cleanup |
| `--detailed`         | Show detailed status information      | false   | status            |

## Troubleshooting

### Common Issues and Solutions

#### 1. Rollback Fails with "No messages to rollback"

**Cause:** No messages exist in the chat ID-based structure.

**Solution:**
```javascript
// Check comprehensive status
const status = await getComprehensiveStatus(selectedChat);
console.log('Status:', status.status);

if (status.status === 'not_migrated') {
    console.log('Messages are already in sender ID structure');
} else if (status.status === 'no_data') {
    console.log('No messages exist in either structure');
}
```

#### 2. Cleanup Fails with "Migration verification failed"

**Cause:** Migration integrity issues detected.

**Solution:**
```javascript
// Perform detailed verification
const verification = await verifyMigrationIntegrity(selectedChat);
console.log('Verification details:', verification);

// Address specific issues
if (verification.missingInDestination.length > 0) {
    console.log('Re-run migration for missing messages');
}

if (verification.dataIntegrityIssues.length > 0) {
    console.log('Fix data integrity issues before cleanup');
}
```

#### 3. Batch Operations Timeout

**Cause:** Large datasets or slow network connections.

**Solution:**
```javascript
// Reduce batch size
const result = await rollbackToChatIdStructure(selectedChat, {
    batchSize: 25, // Reduced from default 50
    dryRun: false
});

// Or process in smaller chunks manually
```

#### 4. Permission Errors

**Cause:** Insufficient Firebase permissions.

**Solution:**
- Verify Firebase authentication
- Check Firestore security rules
- Ensure proper service account permissions

#### 5. Inconsistent Collection Paths

**Cause:** Chat type determination issues.

**Solution:**
```javascript
import { determineChatType, getDualWritePaths } from '../../services/firebase/collectionPaths';

// Debug chat type detection
const chatType = determineChatType(selectedChat);
console.log('Detected chat type:', chatType);

const paths = getDualWritePaths(selectedChat, chatType);
console.log('Collection paths:', paths);
```

### Recovery Procedures

#### If Rollback Fails Partially

1. **Assess Current State**
   ```javascript
   const status = await getComprehensiveStatus(selectedChat);
   console.log('Current state:', status);
   ```

2. **Complete Rollback Manually**
   ```javascript
   // Continue rollback with error handling
   const result = await rollbackToChatIdStructure(selectedChat, {
       dryRun: false,
       batchSize: 25, // Smaller batches
       verifyIntegrity: false // Skip if causing issues
   });
   ```

3. **Verify and Clean Up**
   ```javascript
   // Verify final state
   const finalStatus = await getComprehensiveStatus(selectedChat);

   // Clean up any remaining inconsistencies
   if (finalStatus.canCleanup) {
       await cleanupOldSenderIdPaths(selectedChat, { force: true });
   }
   ```

#### If Cleanup Fails Partially

1. **Check What Was Cleaned**
   ```javascript
   const status = await getComprehensiveStatus(selectedChat);
   console.log('Remaining old messages:', status.legacyCount);
   ```

2. **Resume Cleanup**
   ```javascript
   // Continue cleanup
   const result = await cleanupOldSenderIdPaths(selectedChat, {
       dryRun: false,
       verifyMigration: false, // Skip if verified previously
       batchSize: 25
   });
   ```

## Best Practices

### General Guidelines

1. **Always Use Dry Run First**
   - Test all operations with `--dry-run` flag
   - Review results before executing actual operations
   - Understand the scope and impact

2. **Backup Before Operations**
   - Export collections before rollback or cleanup
   - Document current system state
   - Prepare recovery procedures

3. **Monitor System Health**
   - Check application performance during operations
   - Monitor error rates and user experience
   - Have rollback plan ready

4. **Verify Data Integrity**
   - Always verify migration before cleanup
   - Check data consistency after operations
   - Validate application functionality

### Rollback Best Practices

1. **Emergency Use Only**
   - Reserve rollback for critical issues
   - Document reasons for rollback
   - Plan forward migration strategy

2. **Staged Approach**
   - Start with single chat rollback
   - Verify success before bulk operations
   - Monitor system stability

3. **Communication**
   - Notify stakeholders of rollback plans
   - Document rollback procedures
   - Plan user communication if needed

### Cleanup Best Practices

1. **Wait for Stability**
   - Allow system to run stably with new structure
   - Monitor for several days/weeks before cleanup
   - Ensure no critical issues exist

2. **Gradual Cleanup**
   - Clean up in batches over time
   - Monitor system performance during cleanup
   - Pause if issues are detected

3. **Retention Policy**
   - Consider keeping backups of old collections
   - Define retention period for old data
   - Plan final deletion timeline

### Monitoring and Logging

1. **Operation Logging**
   - All operations are automatically logged
   - Monitor logs for errors and performance
   - Use logs for troubleshooting

2. **Performance Monitoring**
   - Track operation duration and throughput
   - Monitor Firebase quota usage
   - Optimize batch sizes based on performance

3. **Error Tracking**
   - Monitor error rates during operations
   - Set up alerts for critical failures
   - Implement automatic retry mechanisms

## Conclusion

Rollback and cleanup procedures are critical components of the Firebase collection migration process. By following these documented procedures and best practices, you can safely manage the transition between collection structures while maintaining data integrity and system stability.

Remember:
- **Rollback is for emergencies only**
- **Always verify before cleanup**
- **Use dry runs to test operations**
- **Monitor system health throughout**
- **Document all operations and decisions**

For additional support or questions, refer to the migration monitoring logs and consult with the development team.
