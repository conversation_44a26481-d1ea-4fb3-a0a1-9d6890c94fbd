/**
 * Firebase Query Performance Monitor
 *
 * This utility provides comprehensive monitoring and logging for Firebase query performance.
 * It tracks query execution times, cache hit rates, and identifies slow queries.
 */

// Performance metrics storage
const performanceMetrics = {
    queries: new Map(),
    slowQueries: [],
    cacheStats: {
        hits: 0,
        misses: 0,
        totalRequests: 0
    },
    queryStats: new Map()
};

// Configuration
const SLOW_QUERY_THRESHOLD = 1000; // 1 second
const MAX_SLOW_QUERIES_STORED = 50;
const METRICS_RETENTION_TIME = 24 * 60 * 60 * 1000; // 24 hours

/**
 * Start monitoring a query
 * @param {string} queryId - Unique identifier for the query
 * @param {Object} metadata - Additional metadata about the query
 * @returns {string} - The query ID for ending the timer
 */
export const startQueryTimer = (queryId, metadata = {}) => {
    const startTime = performance.now();
    const timestamp = new Date().toISOString();

    performanceMetrics.queries.set(queryId, {
        queryId,
        startTime,
        timestamp,
        metadata
    });

    console.log(`[QUERY_START] ${queryId}:`, { timestamp, metadata });
    return queryId;
};

/**
 * End monitoring a query and log performance metrics
 * @param {string} queryId - The query ID from startQueryTimer
 * @param {Object} result - Query result metadata (count, source, etc.)
 * @returns {Object} - Performance metrics for this query
 */
export const endQueryTimer = (queryId, result = {}) => {
    const queryData = performanceMetrics.queries.get(queryId);

    if (!queryData) {
        console.warn(`[QUERY_MONITOR] No timer found for query: ${queryId}`);
        return null;
    }

    const endTime = performance.now();
    const duration = endTime - queryData.startTime;
    const roundedDuration = Math.round(duration * 100) / 100;

    const metrics = {
        queryId,
        duration: roundedDuration,
        startTime: queryData.timestamp,
        endTime: new Date().toISOString(),
        metadata: queryData.metadata,
        result
    };

    // Log the performance
    console.log(`[QUERY_END] ${queryId}:`, metrics);

    // Track slow queries
    if (duration > SLOW_QUERY_THRESHOLD) {
        console.warn(`[SLOW_QUERY] ${queryId} took ${roundedDuration}ms:`, metrics);

        performanceMetrics.slowQueries.push({
            ...metrics,
            severity: duration > 3000 ? 'critical' : 'warning'
        });

        // Keep only the most recent slow queries
        if (performanceMetrics.slowQueries.length > MAX_SLOW_QUERIES_STORED) {
            performanceMetrics.slowQueries = performanceMetrics.slowQueries.slice(-MAX_SLOW_QUERIES_STORED);
        }
    }

    // Update query statistics
    const queryType = queryData.metadata.queryType || 'unknown';
    if (!performanceMetrics.queryStats.has(queryType)) {
        performanceMetrics.queryStats.set(queryType, {
            count: 0,
            totalDuration: 0,
            averageDuration: 0,
            minDuration: Infinity,
            maxDuration: 0
        });
    }

    const stats = performanceMetrics.queryStats.get(queryType);
    stats.count++;
    stats.totalDuration += duration;
    stats.averageDuration = stats.totalDuration / stats.count;
    stats.minDuration = Math.min(stats.minDuration, duration);
    stats.maxDuration = Math.max(stats.maxDuration, duration);

    // Clean up
    performanceMetrics.queries.delete(queryId);

    return metrics;
};

/**
 * Record cache hit or miss
 * @param {boolean} isHit - Whether this was a cache hit
 * @param {string} cacheKey - The cache key
 * @param {Object} metadata - Additional metadata
 */
export const recordCacheAccess = (isHit, cacheKey, metadata = {}) => {
    performanceMetrics.cacheStats.totalRequests++;

    if (isHit) {
        performanceMetrics.cacheStats.hits++;
        console.log(`[CACHE_HIT] ${cacheKey}:`, metadata);
    } else {
        performanceMetrics.cacheStats.misses++;
        console.log(`[CACHE_MISS] ${cacheKey}:`, metadata);
    }
};

/**
 * Get current performance statistics
 * @returns {Object} - Comprehensive performance statistics
 */
export const getPerformanceStats = () => {
    const cacheHitRate = performanceMetrics.cacheStats.totalRequests > 0
        ? (performanceMetrics.cacheStats.hits / performanceMetrics.cacheStats.totalRequests * 100).toFixed(2)
        : 0;

    const queryStatsArray = Array.from(performanceMetrics.queryStats.entries()).map(([type, stats]) => ({
        queryType: type,
        ...stats,
        averageDuration: Math.round(stats.averageDuration * 100) / 100,
        minDuration: stats.minDuration === Infinity ? 0 : Math.round(stats.minDuration * 100) / 100,
        maxDuration: Math.round(stats.maxDuration * 100) / 100
    }));

    return {
        activeQueries: performanceMetrics.queries.size,
        cacheStats: {
            ...performanceMetrics.cacheStats,
            hitRate: `${cacheHitRate}%`
        },
        slowQueries: performanceMetrics.slowQueries.slice(-10), // Last 10 slow queries
        queryStats: queryStatsArray,
        summary: {
            totalSlowQueries: performanceMetrics.slowQueries.length,
            criticalSlowQueries: performanceMetrics.slowQueries.filter(q => q.severity === 'critical').length,
            mostSlowQueryType: queryStatsArray.length > 0
                ? queryStatsArray.reduce((prev, current) =>
                    (prev.maxDuration > current.maxDuration) ? prev : current
                ).queryType
                : 'none'
        }
    };
};

/**
 * Log performance summary to console
 */
export const logPerformanceSummary = () => {
    const stats = getPerformanceStats();

    console.group('[QUERY_PERFORMANCE_SUMMARY]');
    console.log('Active Queries:', stats.activeQueries);
    console.log('Cache Hit Rate:', stats.cacheStats.hitRate);
    console.log('Total Slow Queries:', stats.summary.totalSlowQueries);
    console.log('Critical Slow Queries:', stats.summary.criticalSlowQueries);
    console.log('Query Statistics:', stats.queryStats);

    if (stats.slowQueries.length > 0) {
        console.warn('Recent Slow Queries:', stats.slowQueries);
    }

    console.groupEnd();
};

/**
 * Clear old performance data
 */
export const cleanupOldMetrics = () => {
    const cutoffTime = Date.now() - METRICS_RETENTION_TIME;

    // Clean up slow queries older than retention time
    performanceMetrics.slowQueries = performanceMetrics.slowQueries.filter(query => {
        const queryTime = new Date(query.startTime).getTime();
        return queryTime > cutoffTime;
    });

    console.log('[QUERY_MONITOR] Cleaned up old performance metrics');
};

/**
 * Monitor a query function execution
 * @param {string} queryId - Unique identifier for the query
 * @param {Function} queryFunction - The function to monitor
 * @param {Object} metadata - Additional metadata
 * @returns {Promise} - The result of the query function
 */
export const monitorQuery = async (queryId, queryFunction, metadata = {}) => {
    startQueryTimer(queryId, metadata);

    try {
        const result = await queryFunction();
        endQueryTimer(queryId, {
            success: true,
            resultCount: Array.isArray(result) ? result.length : 1
        });
        return result;
    } catch (error) {
        endQueryTimer(queryId, {
            success: false,
            error: error.message
        });
        throw error;
    }
};

/**
 * Create a performance monitoring wrapper for Firebase queries
 * @param {string} queryType - Type of query (e.g., 'messages', 'comments')
 * @returns {Object} - Object with monitoring functions
 */
export const createQueryMonitor = (queryType) => {
    return {
        startTimer: (operation, metadata = {}) => {
            const queryId = `${queryType}_${operation}_${Date.now()}`;
            return startQueryTimer(queryId, { queryType, operation, ...metadata });
        },

        endTimer: (queryId, result = {}) => {
            return endQueryTimer(queryId, result);
        },

        monitor: async (operation, queryFunction, metadata = {}) => {
            const queryId = `${queryType}_${operation}_${Date.now()}`;
            return monitorQuery(queryId, queryFunction, { queryType, operation, ...metadata });
        }
    };
};

// Auto-cleanup old metrics every hour
setInterval(cleanupOldMetrics, 60 * 60 * 1000);

// Export performance metrics for debugging
if (typeof window !== 'undefined') {
    window.firebaseQueryPerformance = {
        getStats: getPerformanceStats,
        logSummary: logPerformanceSummary,
        cleanup: cleanupOldMetrics
    };
}

export default {
    startQueryTimer,
    endQueryTimer,
    recordCacheAccess,
    getPerformanceStats,
    logPerformanceSummary,
    cleanupOldMetrics,
    monitorQuery,
    createQueryMonitor
};
