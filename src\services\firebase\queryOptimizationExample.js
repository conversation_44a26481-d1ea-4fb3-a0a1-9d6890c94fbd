/**
 * Firebase Query Optimization Example
 *
 * This file demonstrates how to use the optimized Firebase queries
 * for consistent chat ID-based collection paths.
 */

import {
    fetchOptimizedMessages,
    fetchOptimizedComments,
    fetchChatDataOptimized,
    listenToMessagesOptimized,
    listenToCommentsOptimized,
    listenToWhatsAppMessagesOptimized,
    getQueryPerformanceStats,
    clearQueryCache
} from './queryOptimization';

import { getChatIdentifier, determineChatType } from './collectionPaths';

/**
 * Example: Fetch messages with optimization
 */
export const exampleFetchMessages = async (selectedChat) => {
    try {
        // Determine chat type and get consistent chat ID
        const chatType = determineChatType(selectedChat);
        const chatId = getChatIdentifier(selectedChat, chatType);

        console.log('[EXAMPLE] Fetching optimized messages:', { chatId, chatType });

        // Fetch messages with caching and performance monitoring
        const messages = await fetchOptimizedMessages(chatId, chatType, {
            limit: 50,
            orderDirection: 'asc',
            useCache: true
        });

        console.log('[EXAMPLE] Fetched messages:', messages.length);
        return messages;
    } catch (error) {
        console.error('[EXAMPLE] Error fetching messages:', error);
        throw error;
    }
};

/**
 * Example: Fetch both messages and comments efficiently
 */
export const exampleFetchChatData = async (selectedChat) => {
    try {
        const chatType = determineChatType(selectedChat);
        const chatId = getChatIdentifier(selectedChat, chatType);

        console.log('[EXAMPLE] Fetching combined chat data:', { chatId, chatType });

        // Fetch both messages and comments in parallel
        const { messages, comments } = await fetchChatDataOptimized(chatId, chatType, {
            messagesLimit: 50,
            commentsLimit: 100,
            useCache: true
        });

        console.log('[EXAMPLE] Fetched chat data:', {
            messagesCount: messages.length,
            commentsCount: comments.length
        });

        return { messages, comments };
    } catch (error) {
        console.error('[EXAMPLE] Error fetching chat data:', error);
        throw error;
    }
};

/**
 * Example: Set up optimized real-time listeners
 */
export const exampleSetupListeners = (selectedChat, onMessagesUpdate, onCommentsUpdate) => {
    try {
        const chatType = determineChatType(selectedChat);
        const chatId = getChatIdentifier(selectedChat, chatType);

        console.log('[EXAMPLE] Setting up optimized listeners:', { chatId, chatType });

        // Set up messages listener
        const unsubscribeMessages = listenToMessagesOptimized(
            chatId,
            chatType,
            (messages) => {
                console.log('[EXAMPLE] Messages updated:', messages.length);
                onMessagesUpdate(messages);
            },
            { limit: 50, orderDirection: 'asc' }
        );

        // Set up comments listener
        const unsubscribeComments = listenToCommentsOptimized(
            chatId,
            chatType,
            (comments) => {
                console.log('[EXAMPLE] Comments updated:', comments.length);
                onCommentsUpdate(comments);
            },
            { limit: 100, orderDirection: 'asc' }
        );

        // Return combined unsubscribe function
        return () => {
            unsubscribeMessages();
            unsubscribeComments();
            console.log('[EXAMPLE] Unsubscribed from all listeners');
        };
    } catch (error) {
        console.error('[EXAMPLE] Error setting up listeners:', error);
        return () => { }; // Return no-op function
    }
};

/**
 * Example: WhatsApp optimized listener
 */
export const exampleWhatsAppListener = (selectedPhone, onMessagesUpdate) => {
    try {
        const businessPhoneNumber = selectedPhone.display_phone_number
            .toString()
            .trim()
            .replace(/^\+|\s+/g, "");

        console.log('[EXAMPLE] Setting up WhatsApp listener:', businessPhoneNumber);

        const unsubscribe = listenToWhatsAppMessagesOptimized(
            businessPhoneNumber,
            (messages) => {
                console.log('[EXAMPLE] WhatsApp messages updated:', messages.length);
                onMessagesUpdate(messages);
            },
            { limit: 100, orderDirection: 'desc' }
        );

        return unsubscribe;
    } catch (error) {
        console.error('[EXAMPLE] Error setting up WhatsApp listener:', error);
        return () => { };
    }
};

/**
 * Example: Performance monitoring
 */
export const examplePerformanceMonitoring = () => {
    try {
        const stats = getQueryPerformanceStats();

        console.log('[EXAMPLE] Query Performance Statistics:');
        console.log('- Active Queries:', stats.activeQueries);
        console.log('- Cache Size:', stats.cacheSize);
        console.log('- Cache Hit Rate:', stats.cacheHitRate);

        return stats;
    } catch (error) {
        console.error('[EXAMPLE] Error getting performance stats:', error);
        return null;
    }
};

/**
 * Example: Cache management
 */
export const exampleCacheManagement = () => {
    try {
        console.log('[EXAMPLE] Clearing query cache');
        clearQueryCache();

        // Get stats after clearing
        const stats = getQueryPerformanceStats();
        console.log('[EXAMPLE] Cache cleared. New cache size:', stats.cacheSize);

        return true;
    } catch (error) {
        console.error('[EXAMPLE] Error managing cache:', error);
        return false;
    }
};

/**
 * Example: Complete chat integration
 */
export const exampleCompleteIntegration = async (selectedChat, callbacks) => {
    try {
        const chatType = determineChatType(selectedChat);
        const chatId = getChatIdentifier(selectedChat, chatType);

        console.log('[EXAMPLE] Starting complete chat integration:', { chatId, chatType });

        // 1. Fetch initial data
        const initialData = await fetchChatDataOptimized(chatId, chatType, {
            messagesLimit: 50,
            commentsLimit: 100,
            useCache: true
        });

        // 2. Set up real-time listeners
        const unsubscribeMessages = listenToMessagesOptimized(
            chatId,
            chatType,
            callbacks.onMessagesUpdate,
            { limit: 50 }
        );

        const unsubscribeComments = listenToCommentsOptimized(
            chatId,
            chatType,
            callbacks.onCommentsUpdate,
            { limit: 100 }
        );

        // 3. Monitor performance
        const performanceInterval = setInterval(() => {
            const stats = getQueryPerformanceStats();
            if (callbacks.onPerformanceUpdate) {
                callbacks.onPerformanceUpdate(stats);
            }
        }, 30000); // Every 30 seconds

        console.log('[EXAMPLE] Complete integration setup successful');

        // Return cleanup function
        return () => {
            unsubscribeMessages();
            unsubscribeComments();
            clearInterval(performanceInterval);
            console.log('[EXAMPLE] Complete integration cleaned up');
        };

    } catch (error) {
        console.error('[EXAMPLE] Error in complete integration:', error);
        throw error;
    }
};

/**
 * Example usage in a React component:
 *
 * ```javascript
 * import { exampleCompleteIntegration } from '../services/firebase/queryOptimizationExample';
 *
 * const ChatComponent = ({ selectedChat }) => {
 *   const [messages, setMessages] = useState([]);
 *   const [comments, setComments] = useState([]);
 *   const [performance, setPerformance] = useState(null);
 *
 *   useEffect(() => {
 *     if (!selectedChat) return;
 *
 *     const cleanup = exampleCompleteIntegration(selectedChat, {
 *       onMessagesUpdate: setMessages,
 *       onCommentsUpdate: setComments,
 *       onPerformanceUpdate: setPerformance
 *     });
 *
 *     return cleanup;
 *   }, [selectedChat]);
 *
 *   return (
 *     <div>
 *       <div>Messages: {messages.length}</div>
 *       <div>Comments: {comments.length}</div>
 *       {performance && (
 *         <div>Cache Hit Rate: {performance.cacheHitRate}</div>
 *       )}
 *     </div>
 *   );
 * };
 * ```
 */

export default {
    exampleFetchMessages,
    exampleFetchChatData,
    exampleSetupListeners,
    exampleWhatsAppListener,
    examplePerformanceMonitoring,
    exampleCacheManagement,
    exampleCompleteIntegration
};
