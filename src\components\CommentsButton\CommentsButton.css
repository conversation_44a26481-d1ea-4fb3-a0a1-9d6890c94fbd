/* CommentsButton Component Styles */
.comments-button {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border: none;
  border-radius: 50%;
  background-color: transparent;
  color: #6c757d;
  cursor: pointer;
  transition: all 0.3s ease;
  z-index: 1;
}

.comments-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(108, 117, 125, 0.1);
  border-radius: 50%;
  transform: scale(0);
  transition: transform 0.3s ease;
  z-index: -1;
}

.comments-button:hover::before {
  transform: scale(1);
}

.comments-button:hover {
  color: #0d6efd;
  transform: scale(1.05);
}

.comments-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.comments-button:disabled:hover {
  transform: none;
  color: #6c757d;
}

.comments-button:disabled::before {
  transform: scale(0);
}

/* Active state when modal is open */
.comments-button.active {
  color: #0d6efd;
  background-color: rgba(13, 110, 253, 0.1);
}

.comments-button.active::before {
  transform: scale(1);
  background-color: rgba(13, 110, 253, 0.1);
}

/* Comments icon */
.comments-icon {
  transition: color 0.3s ease;
}

/* Notification badge */
.notification-badge {
  position: absolute;
  top: -2px;
  right: -2px;
  background-color: #dc3545;
  color: white;
  font-size: 10px;
  font-weight: 600;
  min-width: 18px;
  height: 18px;
  border-radius: 9px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 4px;
  line-height: 1;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
  z-index: 2;
}

/* Pulse animation for unread comments */
.comments-button.has-unread {
  animation: commentsPulse 2s infinite;
}

.comments-button.has-unread .comments-icon {
  color: #0d6efd;
}

@keyframes commentsPulse {
  0% {
    box-shadow: 0 0 0 0 rgba(13, 110, 253, 0.4);
  }
  50% {
    box-shadow: 0 0 0 8px rgba(13, 110, 253, 0.1);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(13, 110, 253, 0);
  }
}

/* Responsive design for mobile devices */
@media (max-width: 768px) {
  .comments-button {
    width: 36px;
    height: 36px;
  }

  .comments-icon {
    font-size: 16px;
  }

  .notification-badge {
    font-size: 9px;
    min-width: 16px;
    height: 16px;
    border-radius: 8px;
    top: -1px;
    right: -1px;
  }
}

@media (max-width: 576px) {
  .comments-button {
    width: 32px;
    height: 32px;
  }

  .comments-icon {
    font-size: 14px;
  }

  .notification-badge {
    font-size: 8px;
    min-width: 14px;
    height: 14px;
    border-radius: 7px;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .comments-button {
    border: 1px solid currentColor;
  }

  .notification-badge {
    border: 1px solid white;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .comments-button,
  .comments-button::before,
  .comments-icon {
    transition: none;
  }

  .comments-button.has-unread {
    animation: none;
  }

  .comments-button:hover {
    transform: none;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .comments-button {
    color: #adb5bd;
  }

  .comments-button::before {
    background-color: rgba(173, 181, 189, 0.1);
  }

  .comments-button:hover {
    color: #4dabf7;
  }

  .comments-button:hover::before {
    background-color: rgba(77, 171, 247, 0.1);
  }

  .comments-button.active {
    color: #4dabf7;
    background-color: rgba(77, 171, 247, 0.1);
  }

  .comments-button.active::before {
    background-color: rgba(77, 171, 247, 0.1);
  }

  .comments-button.has-unread .comments-icon {
    color: #4dabf7;
  }

  @keyframes commentsPulse {
    0% {
      box-shadow: 0 0 0 0 rgba(77, 171, 247, 0.4);
    }
    50% {
      box-shadow: 0 0 0 8px rgba(77, 171, 247, 0.1);
    }
    100% {
      box-shadow: 0 0 0 0 rgba(77, 171, 247, 0);
    }
  }
}

/* Focus visible for better keyboard navigation */
.comments-button:focus-visible {
  outline: 2px solid #007bff;
  outline-offset: 2px;
  border-radius: 50%;
}
