/**
 * Optimized Custom hook for managing real-time chat functionality
 *
 * This hook provides an efficient interface for setting up and managing
 * real-time listeners with automatic cleanup, debouncing, and resource optimization.
 */

import { useEffect, useCallback, useRef, useMemo } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
  startRealtimeChatsListener,
  startRealtimeMessagesListener,
  startRealtimeCommentsListener,
  stopAllRealtimeListeners,
  markCommentsAsReadRealtime,
  getRealtimeListenerStatus
} from '../redux/features/metaBusinessChatSlice';

/**
 * Optimized Hook for managing real-time chat functionality
 * @param {Object} options - Configuration options
 * @returns {Object} - Real-time chat utilities
 */
export const useRealtimeChat = (options = {}) => {
  const dispatch = useDispatch();
  const activeListenersRef = useRef(new Set());
  const debounceTimersRef = useRef(new Map());
  const lastListenerConfigRef = useRef(null);

  // Redux selectors with memoization
  const selectedPage = useSelector(state => state.metaBusinessSuite?.selectedPage);
  const selectedChat = useSelector(state => state.metaBusinessSuite?.selectedChat);
  const selectedWhatsappChat = useSelector(state => state.metaBusinessSuite?.selectedWhatsappChat);
  const currentUser = useSelector(state => state.auth?.user);
  const comments = useSelector(state => state.metaBusinessSuite?.comments);
  const commentsModal = useSelector(state => state.metaBusinessSuite?.commentsModal);

  // Memoized options to prevent unnecessary re-renders
  const memoizedOptions = useMemo(() => ({
    autoStartChatsListener: options.autoStartChatsListener ?? true,
    autoStartMessagesListener: options.autoStartMessagesListener ?? true,
    autoStartCommentsListener: options.autoStartCommentsListener ?? false,
    autoMarkCommentsAsRead: options.autoMarkCommentsAsRead ?? true,
    cleanupOnUnmount: options.cleanupOnUnmount ?? true,
    debounceDelay: options.debounceDelay ?? 300, // Debounce listener starts
    enableLogging: options.enableLogging ?? false // Disable excessive logging by default
  }), [options]);

  // Debounced function helper
  const debounce = useCallback((key, fn, delay) => {
    const existingTimer = debounceTimersRef.current.get(key);
    if (existingTimer) {
      clearTimeout(existingTimer);
    }

    const timer = setTimeout(fn, delay);
    debounceTimersRef.current.set(key, timer);

    return () => {
      clearTimeout(timer);
      debounceTimersRef.current.delete(key);
    };
  }, []);

  /**
   * Optimized start listening to all chats for the current page
   */
  const startChatsListener = useCallback(async () => {
    if (!selectedPage?.page_id) {
      if (memoizedOptions.enableLogging) {
        console.warn('[REALTIME] Cannot start chats listener: no page selected');
      }
      return;
    }

    const listenerId = `chats_${selectedPage.page_id}`;

    // Skip if already active
    if (activeListenersRef.current.has(listenerId)) {
      if (memoizedOptions.enableLogging) {
        console.log(`[REALTIME] Chats listener already active for page ${selectedPage.page_id}`);
      }
      return;
    }

    // Debounce to prevent rapid listener creation
    debounce('chats', async () => {
      try {
        await dispatch(startRealtimeChatsListener({
          pageId: selectedPage.page_id
        })).unwrap();

        activeListenersRef.current.add(listenerId);
        if (memoizedOptions.enableLogging) {
          console.log(`[REALTIME] Started chats listener for page ${selectedPage.page_id}`);
        }
      } catch (error) {
        console.error('[REALTIME] Failed to start chats listener:', error);
      }
    }, memoizedOptions.debounceDelay);
  }, [dispatch, selectedPage?.page_id, memoizedOptions, debounce]);

  /**
   * Start listening to messages for the current chat using sender ID
   */
  const startMessagesListener = useCallback(async () => {
    const currentChat = selectedChat || selectedWhatsappChat;
    if (!currentChat || !selectedPage?.page_id) {
      console.warn('[REALTIME] Cannot start messages listener: no chat or page selected');
      return;
    }

    // Get sender ID for Firebase path (same as messages path)
    const { getSenderId, determineChatType } = await import('../services/firebase/collectionPaths');

    const chatType = selectedWhatsappChat ? 'whatsapp' :
      selectedChat?.flage === 'instagram' ? 'instagram' : 'messenger';

    const senderId = getSenderId(currentChat, chatType);

    console.log(`[REALTIME_DEBUG] Starting messages listener:`, {
      chatType,
      senderId,
      pageId: selectedPage.page_id,
      currentChat: currentChat,
      selectedWhatsappChat: !!selectedWhatsappChat,
      selectedChat: !!selectedChat
    });

    if (!senderId) {
      console.warn('[REALTIME] Cannot start messages listener: invalid sender ID');
      return;
    }

    try {
      await dispatch(startRealtimeMessagesListener({
        chatId: senderId, // Pass sender ID as chatId for Firebase path
        chatType,
        pageId: selectedPage.page_id
      })).unwrap();

      activeListenersRef.current.add(`messages_${selectedPage.page_id}_${senderId}`);
      console.log(`[REALTIME] Started messages listener for sender ${senderId}`);
    } catch (error) {
      console.error('[REALTIME] Failed to start messages listener:', error);
    }
  }, [dispatch, selectedChat, selectedWhatsappChat, selectedPage?.page_id]);

  /**
   * Start listening to comments for the current chat using sender ID
   */
  const startCommentsListener = useCallback(async () => {
    const currentChat = selectedChat || selectedWhatsappChat;
    if (!currentChat || !selectedPage?.page_id || !currentUser) {
      console.warn('[REALTIME] Cannot start comments listener: missing requirements');
      return;
    }

    // Get sender ID for Firebase path (same as messages path)
    const { getSenderId, determineChatType } = await import('../services/firebase/collectionPaths');

    const chatType = selectedWhatsappChat ? 'whatsapp' :
      selectedChat?.flage === 'instagram' ? 'instagram' : 'messenger';

    const senderId = getSenderId(currentChat, chatType);

    if (!senderId) {
      console.warn('[REALTIME] Cannot start comments listener: invalid sender ID');
      return;
    }

    try {
      await dispatch(startRealtimeCommentsListener({
        chatId: senderId, // Pass sender ID as chatId for Firebase path
        chatType,
        pageId: selectedPage.page_id,
        autoMarkAsRead: memoizedOptions.autoMarkCommentsAsRead,
        autoScrollToBottom: false // Disabled: Don't auto-scroll when new comments arrive
      })).unwrap();

      activeListenersRef.current.add(`comments_${selectedPage.page_id}_${senderId}`);
      console.log(`[REALTIME] Started comments listener for sender ${senderId}`);
    } catch (error) {
      console.error('[REALTIME] Failed to start comments listener:', error);
    }
  }, [dispatch, selectedChat, selectedWhatsappChat, selectedPage?.page_id, currentUser, memoizedOptions.autoMarkCommentsAsRead]);

  /**
   * Mark specific comments as read
   */
  const markCommentsAsRead = useCallback(async (commentIds) => {
    const currentChat = selectedChat || selectedWhatsappChat;
    if (!currentChat || !selectedPage?.page_id || !currentUser || !commentIds?.length) {
      console.warn('[REALTIME] Cannot mark comments as read: missing requirements');
      return;
    }

    const chatType = selectedWhatsappChat ? 'whatsapp' :
      selectedChat?.flage === 'instagram' ? 'instagram' : 'messenger';

    const chatId = chatType === 'whatsapp'
      ? currentChat.sender_phone_number?.toString().trim().replace(/^\+|\s+/g, "")
      : currentChat.id?.toString();

    if (!chatId) {
      console.warn('[REALTIME] Cannot mark comments as read: invalid chat ID');
      return;
    }

    try {
      await dispatch(markCommentsAsReadRealtime({
        chatId,
        chatType,
        pageId: selectedPage.page_id,
        commentIds
      })).unwrap();

      console.log(`[REALTIME] Marked ${commentIds.length} comments as read`);
    } catch (error) {
      console.error('[REALTIME] Failed to mark comments as read:', error);
    }
  }, [dispatch, selectedChat, selectedWhatsappChat, selectedPage?.page_id, currentUser]);

  /**
   * Stop all real-time listeners
   */
  const stopAllListeners = useCallback(async () => {
    try {
      await dispatch(stopAllRealtimeListeners()).unwrap();
      activeListenersRef.current.clear();
      console.log('[REALTIME] Stopped all listeners');
    } catch (error) {
      console.error('[REALTIME] Failed to stop listeners:', error);
    }
  }, [dispatch]);

  /**
   * Get current listener status
   */
  const getListenerStatus = useCallback(async () => {
    try {
      const status = await dispatch(getRealtimeListenerStatus()).unwrap();
      return status;
    } catch (error) {
      console.error('[REALTIME] Failed to get listener status:', error);
      return null;
    }
  }, [dispatch]);

  // Auto-start listeners based on options and state changes
  useEffect(() => {
    if (memoizedOptions.autoStartChatsListener && selectedPage?.page_id) {
      startChatsListener();
    }
  }, [memoizedOptions.autoStartChatsListener, selectedPage?.page_id, startChatsListener]);

  useEffect(() => {
    if (memoizedOptions.autoStartMessagesListener && (selectedChat || selectedWhatsappChat)) {
      startMessagesListener();
    }
  }, [memoizedOptions.autoStartMessagesListener, selectedChat, selectedWhatsappChat, startMessagesListener]);

  useEffect(() => {
    if (memoizedOptions.autoStartCommentsListener && commentsModal?.isOpen) {
      startCommentsListener();
    }
  }, [memoizedOptions.autoStartCommentsListener, commentsModal?.isOpen, startCommentsListener]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (memoizedOptions.cleanupOnUnmount) {
        stopAllListeners();
      }
    };
  }, [memoizedOptions.cleanupOnUnmount, stopAllListeners]);

  return {
    // Listener controls
    startChatsListener,
    startMessagesListener,
    startCommentsListener,
    stopAllListeners,

    // Comment utilities
    markCommentsAsRead,

    // Status
    getListenerStatus,
    activeListeners: Array.from(activeListenersRef.current),

    // State - simplified for now, will be updated dynamically
    isChatsListenerActive: activeListenersRef.current.has(`chats_${selectedPage?.page_id}`),
    isMessagesListenerActive: Array.from(activeListenersRef.current).some(listener =>
      listener.startsWith(`messages_${selectedPage?.page_id}_`)
    ),
    isCommentsListenerActive: Array.from(activeListenersRef.current).some(listener =>
      listener.startsWith(`comments_${selectedPage?.page_id}_`)
    ),

    // Data
    comments,
    commentsModal,
    currentUser
  };
};

export default useRealtimeChat;
