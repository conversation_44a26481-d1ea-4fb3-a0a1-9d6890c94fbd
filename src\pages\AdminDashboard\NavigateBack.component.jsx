import React from "react";
import { useNavigate } from "react-router-dom";
import { IoCaretBack } from "react-icons/io5";
import { Tooltip } from "react-tooltip";

function NavigateBackComponent({ title, tooltip }) {
  const navigate = useNavigate();
  return (
    <>
      <div
        className="d-flex my-4 text-white gap-3"
        style={{ cursor: "pointer" }}
        onClick={() => navigate(-1)}
        data-tooltip-id="navigate-back-tooltip"
      >
        <IoCaretBack
          color={"#000"}
          className={"bg-white rounded-circle p-1"}
          size={35}
        />
        <h3>{title}</h3>
      </div>
      <Tooltip id="navigate-back-tooltip" content={tooltip} />
    </>
  );
}

export default NavigateBackComponent;
