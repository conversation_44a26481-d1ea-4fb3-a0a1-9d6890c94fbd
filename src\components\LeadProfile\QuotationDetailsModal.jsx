import Modal from "react-bootstrap/Modal";
import Button from "react-bootstrap/Button";
import { useEffect, useState } from "react";
import { Ratio, Form } from "react-bootstrap";
import { Formik, Form as FormikForm, Field } from "formik";
import { safeNumber } from "../../utils/safe-number";
import leadService from "../../services/leads";
import { toast } from "react-toastify";
import * as Yup from "yup";
import { showSuccessToast, showErrorToast } from "../../utils/toast-success-error";
import { useTranslation } from "react-i18next";
import { getAllLeadStatuses } from "../../config/leadStatusConfig";

// Define constants for quotation statuses to avoid hardcoding
const QUOTATION_STATUS = {
    PENDING: 0,
    ACCEPTED: 1,
    REJECTED: 2
};

const validationSchema = Yup.object({
    price: Yup.number()
        .required("Amount is required")
        .positive("Amount must be greater than zero"),
    status: Yup.string().required("Status is required"),
    refuse_reason: Yup.string().test(
        "requiredForQuotationStatus3",
        "Refuse reason is required",
        function (value) {
            if (Number(this.parent.status) === QUOTATION_STATUS.REJECTED) {
                return !!value;
            }
            return true;
        },
    ),
});

const QuotationDetailsModal = ({ setShowModal, selectedQuotation, showModal, setQuotationsData }) => {
    const { t } = useTranslation();
    const [isRefuseReasonTruncated, setIsRefuseReasonTruncated] = useState(false);
    const [isRefuseReasonExpanded, setIsRefuseReasonExpanded] = useState(false);

    // Check if refuse reason exceeds 4 lines and set truncation
    useEffect(() => {
        // No direct ref usage; manage height based on expansion state
        const element = document.getElementById("refuseReasonTextarea");
        if (element) {
            const thresholdHeight = 100; // Change this if necessary
            const isOverflowing = element.scrollHeight > thresholdHeight;
            setIsRefuseReasonTruncated(isOverflowing);
        }
    }, [selectedQuotation?.refuse_reason]);

    // Update textarea height on expansion state change
    useEffect(() => {
        const element = document.getElementById("refuseReasonTextarea");
        if (element) {
            if (isRefuseReasonExpanded) {
                element.style.height = `${element.scrollHeight}px`;
            } else {
                element.style.height = "100px";
            }
        }
    }, [isRefuseReasonExpanded]);

    const handleSave = async (values) => {
        const updatedFields = {
            price: safeNumber(values.price),
            status: safeNumber(values.status),
            refuse_reason: safeNumber(values.status) === QUOTATION_STATUS.REJECTED ? values.refuse_reason : "",
        };
        try {
            const response = await leadService.updateQuotationApi(updatedFields, selectedQuotation?.id);
            setQuotationsData((prevQuotations) =>
                prevQuotations.map((quotation) => {
                    if (quotation?.id === selectedQuotation?.id) {
                        return { ...quotation, ...updatedFields };
                    }
                    return quotation;
                })
            );
            showSuccessToast(response.message || "Quotation updated successfully", { position: "bottom-right", theme: "dark" });
        } catch (e) {
            showErrorToast(e.response?.data?.message || "Error updating quotation", { position: "bottom-right", theme: "dark" });
        }
    };

    const fileUrl = selectedQuotation?.quotation
        ? process.env.REACT_APP_PROFILE_PIC_ENDPOINT + selectedQuotation.quotation
        : null;

    const isImageFile = (fileName) => /\.(jpg|jpeg|png|gif|bmp)$/i.test(fileName);
    const isDocumentFile = (fileName) => /\.(pdf|doc|docx)$/i.test(fileName);

    return (
        <Formik
            initialValues={{
                price: selectedQuotation?.price || "",
                status: selectedQuotation?.status || "0",
                refuse_reason: selectedQuotation?.refuse_reason || "",
            }}
            validationSchema={validationSchema}
            onSubmit={handleSave}
        >
            {({ values, handleChange, handleBlur, handleSubmit, errors, touched }) => (
                <FormikForm onSubmit={handleSubmit}>
                    <Modal.Header closeButton>
                        <Modal.Title>Quotation Offer</Modal.Title>
                    </Modal.Header>
                    <Modal.Body>
                        {fileUrl && isImageFile(selectedQuotation?.quotation) ? (
                            <img src={fileUrl} alt="Quotation Offer" className="img-fluid" />
                        ) : fileUrl && isDocumentFile(selectedQuotation?.quotation) ? (
                            <div style={{ width: "100%", height: "auto" }}>
                                <Ratio aspectRatio="1x1">
                                    <embed type="application/pdf" src={fileUrl} style={{ width: "100%", height: "100%" }} />
                                </Ratio>
                            </div>
                        ) : (
                            <p>No image or document selected</p>
                        )}

                        <Form>
                            <Form.Group className="mb-3">
                                <Form.Label>Amount</Form.Label>
                                <Field
                                    as={Form.Control}
                                    type="number"
                                    name="price"
                                    onChange={handleChange}
                                    onBlur={handleBlur}
                                    value={values.price}
                                />
                                {touched.price && errors.price && (
                                    <div className="text-danger">{errors.price}</div>
                                )}
                            </Form.Group>

                            <Form.Group className="mb-3">
                                <Form.Label>Status</Form.Label>
                                <Field
                                    as={Form.Select}
                                    name="status"
                                    onChange={handleChange}
                                    onBlur={handleBlur}
                                    value={values.status}
                                >
                                    <option value={QUOTATION_STATUS.PENDING.toString()}>{t('status.pending', 'Pending')}</option>
                                    <option value={QUOTATION_STATUS.ACCEPTED.toString()}>{t('status.accepted', 'Accepted')}</option>
                                    <option value={QUOTATION_STATUS.REJECTED.toString()}>{t('status.rejected', 'Rejected')}</option>
                                </Field>
                                {touched.status && errors.status && (
                                    <div className="text-danger">{errors.status}</div>
                                )}
                            </Form.Group>

                            {safeNumber(values.status) === QUOTATION_STATUS.REJECTED && (
                                <Form.Group className="mb-3">
                                    <Form.Label>Refuse Reason</Form.Label>
                                    <Form.Control
                                        name="refuse_reason"
                                        as="textarea"
                                        id="refuseReasonTextarea"
                                        rows={4}
                                        value={values?.refuse_reason}
                                        onChange={handleChange}
                                        style={{
                                            transition: "height 0.3s ease",
                                            resize: "none",
                                            overflow: "hidden",
                                        }}
                                    />
                                    {touched.refuse_reason && errors.refuse_reason && (
                                        <div className="text-danger">{errors.refuse_reason}</div>
                                    )}
                                    {isRefuseReasonTruncated && (
                                        <div
                                            className="link-primary show-more"
                                            role="button"
                                            onClick={() => setIsRefuseReasonExpanded(!isRefuseReasonExpanded)}
                                            style={{ marginTop: "10px", cursor: "pointer" }}
                                        >
                                            {isRefuseReasonExpanded ? "Show less" : "Show more"}
                                        </div>
                                    )}
                                </Form.Group>
                            )}
                        </Form>
                    </Modal.Body>
                    <Modal.Footer>
                        <Button variant="secondary" onClick={() => setShowModal(false)}>
                            Close
                        </Button>
                        <Button variant="primary" type="submit">
                            Save Changes
                        </Button>
                    </Modal.Footer>
                </FormikForm>
            )}
        </Formik>
    );
};

export default QuotationDetailsModal;
