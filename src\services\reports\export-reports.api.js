import {api} from "../config";
import downloadFile from "../../utils/download-file";
import {cleanData} from "../../utils/clean-data";
import getCurrentFormattedDate from "../../utils/get-current-formatted-date";

async function exportReport(endpoint, params, reportName) {
    try {
        const filteredParams = cleanData(params);
        const response = await api.get(endpoint, {
            params: filteredParams,
            responseType: "blob",
            headers: {
                "Content-Type": "multipart/form-data",
            },
        });
        const fileSafeDate = getCurrentFormattedDate();
        const fileName = `${reportName}-${fileSafeDate}.xlsx`;

        // Trigger the download
        downloadFile(response?.data, fileName);
    } catch (error) {
        console.error(`Error exporting ${reportName} data:`, error);
        throw error;
    }
}

// Specific export functions using the generic function
async function ExportTeamMembersReportsApi(params) {
    return exportReport("reports/team/export", params, "team-members-report");
}

async function ExportLeadAssignmentReportsApi(params) {
    return exportReport("reports/assignment/export", params, "lead-assignment-report");
}

async function ExportSalesPerformanceReportsApi(params) {
    return exportReport("reports/performance/export", params, "sales-performance-report");
}

async function ExportLeadsServiceReportApi(params) {
    return exportReport("reports/leads/service/export", params, "department-report");
}

async function ExportLeadsPlatformReportApi(params) {
    return exportReport("reports/leads/platform/export", params, "department-report");
}

export {
    ExportTeamMembersReportsApi,
    ExportLeadAssignmentReportsApi,
    ExportSalesPerformanceReportsApi,
    ExportLeadsServiceReportApi,
    ExportLeadsPlatformReportApi,
};
