import { useEffect, useRef, useState } from "react";
import { Col, Row } from "react-bootstrap";
import { <PERSON>hn<PERSON>, Bar } from "react-chartjs-2";
import {
  Chart as ChartJS,
  ArcElement,
  Tooltip as ChartTooltip,
  Legend,
  CategoryScale,
  LinearScale,
  BarElement,
} from "chart.js";
import {
  getTopTeamMembersAPI,
  getTotalTeamMembersAPI,
} from "../../services/reports/get-teams-reports.api";
import { useTranslation } from "react-i18next";

ChartJS.register(
  ArcElement,
  ChartTooltip,
  Legend,
  CategoryScale,
  LinearScale,
  BarElement
);

const ChartsForTeamMembers = ({ activeTab }) => {
  const { t } = useTranslation();
  const [pieChartRoundedData, setPieChartRoundedData] = useState([]);
  const [membersData, setMembersData] = useState([]);
  const fetchTeamMembersNumbersController = useRef(null);
  const fetchTopTeamMembersController = useRef(null);
  useEffect(() => {
    if (activeTab !== "members") {
      return;
    }
    const fetchData = async () => {
      if (fetchTeamMembersNumbersController.current) {
        fetchTeamMembersNumbersController.current.abort();
      }
      if (fetchTopTeamMembersController.current) {
        fetchTopTeamMembersController.current.abort();
      }
      fetchTeamMembersNumbersController.current = new AbortController();
      fetchTopTeamMembersController.current = new AbortController();
      const signal = fetchTeamMembersNumbersController.current.signal;
      const topTMSignal = fetchTopTeamMembersController.current.signal;
      try {
        const [totalTeamMembersResponse, topTeamMembersResponse] =
          await Promise.all([
            getTotalTeamMembersAPI({ signal }),
            getTopTeamMembersAPI({ signal: topTMSignal }),
          ]);
        setPieChartRoundedData(totalTeamMembersResponse?.result);
        setMembersData(topTeamMembersResponse?.result);
      } catch (error) {
        if (error.name === "CanceledError") {
          console.log("Fetch aborted");
        } else {
          console.error("Error fetching Lead Assignment Data:", error);
        }
      }
    };
    fetchData();
  }, [activeTab]);

  const getColor = (label) => {
    switch (label) {
      case "Moderator":
        return "#008000";
      case "Accountant":
        return "#BED754";
      case "Sales":
        return "#FDBB1F";
      case "Admin":
        return "#750E21";
      default:
        return "black";
    }
  };

  const getBarColor = (role) => {
    if (role === 3) return "rgb(255,99,132)";
    else return "rgb(72,128,255)";
  };

  return (
    <Row className="justify-content-around justify-content-md-evenly">
      <Col
        lg={6}
        md={12}
        className="content-container team-chart-container"
        // style={{ height: "300px" }}
      >
        <div className="d-flex justify-content-between">
          <div className="fs-5 fw-bold text-muted">
            {t("reports.charts.team.title")}
          </div>
          <div className="fs-5 fw-bold">250</div>
        </div>
        <div
          className="position-relative mx-auto"
          // style={{ width: "200px", height: "200px" }}
          style={{ width: "200px", height: "200px" }}
        >
          <Doughnut
            data={{
              labels: pieChartRoundedData.map((datum) => datum.x),
              datasets: [
                {
                  data: pieChartRoundedData.map((datum) => datum.y),
                  backgroundColor: pieChartRoundedData.map((datum) =>
                    getColor(datum.x)
                  ),
                  borderColor: "#fff",
                  borderWidth: 4,
                },
              ],
            }}
            options={{
              responsive: true,
              plugins: {
                tooltip: {
                  enabled: true,
                  callbacks: {
                    label: function (context) {
                      const label = context.label || "";
                      const value = context.raw || 0;
                      return `${label}: ${value}%`;
                    },
                  },
                  boxPadding: 8,
                },
                legend: {
                  display: false,
                },
              },
              elements: {
                arc: {
                  borderRadius: 20,
                  borderWidth: 4,
                },
              },
              layout: {
                padding: {
                  top: 20,
                  bottom: 20,
                  left: 20,
                  right: 20,
                },
              },
              cutout: "80%",
            }}
          />
          <div
            style={{
              position: "absolute",
              top: "50%",
              left: "50%",
              transform: "translate(-50%, -50%)",
              textAlign: "center",
              fontSize: "20px",
              fontWeight: "bold",
              color: "#333",
              zIndex: 10,
            }}
          >
            {pieChartRoundedData?.length > 0
              ? `${
                  pieChartRoundedData?.reduce((a, b) => (a.y > b.y ? a : b)).x
                }: ${
                  pieChartRoundedData.reduce((a, b) => (a.y > b.y ? a : b)).y
                }%`
              : t("reports.charts.team.noData")}
          </div>
        </div>
        <div
          className="labels-container"
          style={{ maxHeight: "100px", overflowY: "auto" }}
        >
          {pieChartRoundedData.map((datum, index) => (
            <div key={index} className="label-item">
              <span
                className="dot"
                style={{ backgroundColor: getColor(datum.x) }}
              />
              {datum.x}
            </div>
          ))}
        </div>
      </Col>
      <Col
        lg={6}
        md={12}
        className="content-container team-chart-container"
        // style={{ height: "300px" }}
      >
        <div className="fs-5 fw-bold text-muted">
          {t("reports.charts.team.topMembers")}
        </div>
        <div
          className="position-relative mx-auto mt-3"
          style={{ height: "100%" }}
        >
          <Bar
            data={{
              labels: Array.isArray(membersData)
                ? membersData?.map((item) => item.name)
                : membersData?.name,
              datasets: [
                {
                  label: t("reports.charts.team.leads"),
                  backgroundColor: Array.isArray(membersData)
                    ? membersData?.map((item) => getBarColor(item?.role))
                    : getBarColor(membersData?.role),
                  data: Array.isArray(membersData)
                    ? membersData?.map((item) => item.leads)
                    : membersData?.leads,
                  borderRadius: 10,
                  borderSkipped: false,
                  barThickness: 10,
                },
              ],
            }}
            options={{
              responsive: true,
              plugins: {
                tooltip: {
                  enabled: true,
                  callbacks: {
                    label: function (context) {
                      const value = context.raw || 0;
                      return `Leads: ${value}`;
                    },
                  },
                  boxPadding: 8,
                },
                legend: {
                  display: false,
                },
              },
              scales: {
                y: {
                  beginAtZero: true,
                  ticks: {
                    callback: function (value) {
                      return value;
                    },
                  },
                },
              },
            }}
          />
        </div>
      </Col>
      {/*<Col*/}
      {/*  lg={6}*/}
      {/*  md={12}*/}
      {/*  className="content-container team-chart-container"*/}
      {/*  style={{ height: "300px" }}*/}
      {/*>*/}
      {/*  <div className="fs-5 fw-bold text-muted">Performance</div>*/}
      {/*  <CChart*/}
      {/*    type="line"*/}
      {/*    className="line-team-chart mt-3"*/}
      {/*    style={{ height: "100%" }}*/}
      {/*    data={{*/}
      {/*      labels: labels,*/}
      {/*      datasets: datasets,*/}
      {/*      tooltip: {*/}
      {/*        enabled: true,*/}
      {/*        callbacks: {*/}
      {/*          label: function (context) {*/}
      {/*            const label = context.label || "";*/}
      {/*            const value = context.raw || 0;*/}
      {/*            return `${label}: ${value}%`;*/}
      {/*          },*/}
      {/*        },*/}
      {/*      },*/}
      {/*    }}*/}
      {/*    options={chartOptions}*/}
      {/*    plugins={[gradientLinePlugin]}*/}
      {/*  />*/}
      {/*</Col>*/}
    </Row>
  );
};

export default ChartsForTeamMembers;
